package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.entity.SysOperateLog;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.repository.TableSysOperateLogService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 角色状态更新操作日志测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
@Transactional
class RoleStatusUpdateTest {

    @Autowired
    private RoleService roleService;
    
    @Autowired
    private TableRoleService tableRoleService;
    
    @Autowired
    private TableSysOperateLogService tableSysOperateLogService;

    private SysRole testRole;

    @BeforeEach
    void setUp() {
        // 创建测试角色
        testRole = new SysRole();
        testRole.setRoleName("测试角色状态更新");
        testRole.setRoleCode("TEST_STATUS_UPDATE");
        testRole.setRoleType(3);
        testRole.setDescription("用于测试状态更新的角色");
        testRole.setStatus(1); // 初始状态为启用
        testRole.setTenantId(1L);
        testRole.setCreatedTime(new Date());
        testRole.setUpdatedTime(new Date());
        testRole.setCreateBy("test");
        testRole.setUpdateBy("test");
        
        // 插入测试角色
        testRole = tableRoleService.insert(testRole, "test");
        
        log.info("创建测试角色成功，ID: {}, 名称: {}", testRole.getId(), testRole.getRoleName());
    }

    @Test
    @DisplayName("测试角色禁用操作及日志记录")
    void testDisableRole() throws InterruptedException {
        log.info("开始测试角色禁用操作");
        
        // 记录操作前的日志数量
        List<SysOperateLog> beforeLogs = tableSysOperateLogService.findByCondition(null);
        int beforeCount = beforeLogs.size();
        log.info("操作前日志数量：{}", beforeCount);
        
        // 执行角色禁用操作
        roleService.disableRole(testRole.getId());
        
        // 等待异步日志记录完成
        Thread.sleep(2000);
        
        // 验证角色状态是否更新
        SysRole updatedRole = tableRoleService.selectById(testRole.getId());
        assertNotNull(updatedRole, "角色应该存在");
        assertEquals(Integer.valueOf(0), updatedRole.getStatus(), "角色状态应该被更新为禁用");
        
        // 验证操作日志是否记录
        List<SysOperateLog> afterLogs = tableSysOperateLogService.findByCondition(null);
        int afterCount = afterLogs.size();
        log.info("操作后日志数量：{}", afterCount);
        
        assertTrue(afterCount > beforeCount, "应该有新的操作日志记录");
        
        // 查找相关的操作日志
        SysOperateLog operateLog = afterLogs.stream()
            .filter(log -> log.getContent() != null && log.getContent().contains("禁用角色") && log.getContent().contains(testRole.getRoleName()))
            .findFirst()
            .orElse(null);
            
        assertNotNull(operateLog, "应该能找到角色禁用的操作日志");
        assertTrue(operateLog.getContent().contains("禁用角色"), "日志内容应该包含'禁用角色'");
        assertTrue(operateLog.getContent().contains(testRole.getRoleName()), "日志内容应该包含角色名称");
        
        log.info("✅ 角色禁用操作及日志记录测试通过");
        log.info("操作日志内容：{}", operateLog.getContent());
    }

    @Test
    @DisplayName("测试角色启用操作及日志记录")
    void testEnableRole() throws InterruptedException {
        log.info("开始测试角色启用操作");
        
        // 先禁用角色
        roleService.disableRole(testRole.getId());
        Thread.sleep(1000);
        
        // 记录操作前的日志数量
        List<SysOperateLog> beforeLogs = tableSysOperateLogService.findByCondition(null);
        int beforeCount = beforeLogs.size();
        log.info("操作前日志数量：{}", beforeCount);
        
        // 执行角色启用操作
        roleService.enableRole(testRole.getId());
        
        // 等待异步日志记录完成
        Thread.sleep(2000);
        
        // 验证角色状态是否更新
        SysRole updatedRole = tableRoleService.selectById(testRole.getId());
        assertNotNull(updatedRole, "角色应该存在");
        assertEquals(Integer.valueOf(1), updatedRole.getStatus(), "角色状态应该被更新为启用");
        
        // 验证操作日志是否记录
        List<SysOperateLog> afterLogs = tableSysOperateLogService.findByCondition(null);
        int afterCount = afterLogs.size();
        log.info("操作后日志数量：{}", afterCount);
        
        assertTrue(afterCount > beforeCount, "应该有新的操作日志记录");
        
        // 查找相关的操作日志
        SysOperateLog operateLog = afterLogs.stream()
            .filter(log -> log.getContent() != null && log.getContent().contains("启用角色") && log.getContent().contains(testRole.getRoleName()))
            .findFirst()
            .orElse(null);
            
        assertNotNull(operateLog, "应该能找到角色启用的操作日志");
        assertTrue(operateLog.getContent().contains("启用角色"), "日志内容应该包含'启用角色'");
        assertTrue(operateLog.getContent().contains(testRole.getRoleName()), "日志内容应该包含角色名称");
        
        log.info("✅ 角色启用操作及日志记录测试通过");
        log.info("操作日志内容：{}", operateLog.getContent());
    }

    @Test
    @DisplayName("测试通用角色状态更新操作及日志记录")
    void testUpdateRoleStatus() throws InterruptedException {
        log.info("开始测试通用角色状态更新操作");
        
        // 记录操作前的日志数量
        List<SysOperateLog> beforeLogs = tableSysOperateLogService.findByCondition(null);
        int beforeCount = beforeLogs.size();
        log.info("操作前日志数量：{}", beforeCount);
        
        // 执行角色状态更新操作（从启用改为禁用）
        roleService.updateRoleStatus(testRole.getId(), 0);
        
        // 等待异步日志记录完成
        Thread.sleep(2000);
        
        // 验证角色状态是否更新
        SysRole updatedRole = tableRoleService.selectById(testRole.getId());
        assertNotNull(updatedRole, "角色应该存在");
        assertEquals(Integer.valueOf(0), updatedRole.getStatus(), "角色状态应该被更新为禁用");
        
        // 验证操作日志是否记录
        List<SysOperateLog> afterLogs = tableSysOperateLogService.findByCondition(null);
        int afterCount = afterLogs.size();
        log.info("操作后日志数量：{}", afterCount);
        
        assertTrue(afterCount > beforeCount, "应该有新的操作日志记录");
        
        // 查找相关的操作日志
        SysOperateLog operateLog = afterLogs.stream()
            .filter(log -> log.getContent() != null && log.getContent().contains("禁用角色") && log.getContent().contains(testRole.getRoleName()))
            .findFirst()
            .orElse(null);
            
        assertNotNull(operateLog, "应该能找到角色状态更新的操作日志");
        assertTrue(operateLog.getContent().contains("禁用角色"), "日志内容应该包含'禁用角色'");
        assertTrue(operateLog.getContent().contains(testRole.getRoleName()), "日志内容应该包含角色名称");
        
        log.info("✅ 通用角色状态更新操作及日志记录测试通过");
        log.info("操作日志内容：{}", operateLog.getContent());
    }

    @Test
    @DisplayName("测试角色状态无变化时不记录日志")
    void testNoLogWhenStatusUnchanged() throws InterruptedException {
        log.info("开始测试角色状态无变化时不记录日志");
        
        // 记录操作前的日志数量
        List<SysOperateLog> beforeLogs = tableSysOperateLogService.findByCondition(null);
        int beforeCount = beforeLogs.size();
        log.info("操作前日志数量：{}", beforeCount);
        
        // 执行角色状态更新操作（状态不变，仍为启用）
        roleService.updateRoleStatus(testRole.getId(), 1);
        
        // 等待可能的异步日志记录完成
        Thread.sleep(2000);
        
        // 验证角色状态没有变化
        SysRole updatedRole = tableRoleService.selectById(testRole.getId());
        assertNotNull(updatedRole, "角色应该存在");
        assertEquals(Integer.valueOf(1), updatedRole.getStatus(), "角色状态应该保持启用");
        
        // 验证没有新的操作日志记录
        List<SysOperateLog> afterLogs = tableSysOperateLogService.findByCondition(null);
        int afterCount = afterLogs.size();
        log.info("操作后日志数量：{}", afterCount);
        
        assertEquals(beforeCount, afterCount, "状态无变化时不应该记录新的操作日志");
        
        log.info("✅ 角色状态无变化时不记录日志测试通过");
    }

    @Test
    @DisplayName("测试通过updateRole方法更新状态的日志记录")
    void testUpdateRoleWithStatusChange() throws InterruptedException {
        log.info("开始测试通过updateRole方法更新状态的日志记录");
        
        // 记录操作前的日志数量
        List<SysOperateLog> beforeLogs = tableSysOperateLogService.findByCondition(null);
        int beforeCount = beforeLogs.size();
        log.info("操作前日志数量：{}", beforeCount);
        
        // 创建更新对象，只更新状态
        SysRole updateRole = new SysRole();
        updateRole.setId(testRole.getId());
        updateRole.setStatus(0); // 改为禁用
        
        // 执行角色更新操作
        roleService.updateRole(updateRole);
        
        // 等待异步日志记录完成
        Thread.sleep(2000);
        
        // 验证角色状态是否更新
        SysRole updatedRole = tableRoleService.selectById(testRole.getId());
        assertNotNull(updatedRole, "角色应该存在");
        assertEquals(Integer.valueOf(0), updatedRole.getStatus(), "角色状态应该被更新为禁用");
        
        // 验证操作日志是否记录
        List<SysOperateLog> afterLogs = tableSysOperateLogService.findByCondition(null);
        int afterCount = afterLogs.size();
        log.info("操作后日志数量：{}", afterCount);
        
        assertTrue(afterCount > beforeCount, "应该有新的操作日志记录");
        
        // 查找相关的操作日志
        SysOperateLog operateLog = afterLogs.stream()
            .filter(log -> log.getContent() != null && log.getContent().contains("禁用角色") && log.getContent().contains(testRole.getRoleName()))
            .findFirst()
            .orElse(null);
            
        assertNotNull(operateLog, "应该能找到角色状态更新的操作日志");
        assertTrue(operateLog.getContent().contains("禁用角色"), "日志内容应该包含'禁用角色'");
        assertTrue(operateLog.getContent().contains(testRole.getRoleName()), "日志内容应该包含角色名称");
        
        log.info("✅ 通过updateRole方法更新状态的日志记录测试通过");
        log.info("操作日志内容：{}", operateLog.getContent());
    }
}
