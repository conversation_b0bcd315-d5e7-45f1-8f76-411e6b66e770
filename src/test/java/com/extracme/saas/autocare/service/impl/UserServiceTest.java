package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.UserException;
import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;

@ExtendWith(MockitoExtension.class)
@DisplayName("用户服务测试")
class UserServiceTest {

    @Mock
    private TableUserService tableUserService;

    @Mock
    private TableRoleService tableRoleService;

    @Mock
    private TableUserRoleService tableUserRoleService;

    @Mock
    private TableTenantService tableTenantService;

    @InjectMocks
    private UserServiceImpl userService;

    private UserCreateDTO createDTO;
    private UserQueryDTO queryDTO;
    private SysUser mockUser;
    private SysRole mockRole;

    @BeforeEach
    void setUp() {
        // 创建用户DTO
        createDTO = new UserCreateDTO();
        createDTO.setUsername("testuser");
        createDTO.setNickname("Test User");
        createDTO.setMobile("***********");
        createDTO.setEmail("<EMAIL>");
        createDTO.setOrgId("ORG001");
        createDTO.setStatus(1);
        createDTO.setApprovalLevel(2);
        createDTO.setRepairDepotId("DEPOT001");
        createDTO.setAccountType(0);
        createDTO.setRoleIds(Arrays.asList(1L));

        // 查询DTO
        queryDTO = new UserQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 模拟用户数据
        mockUser = new SysUser();
        mockUser.setId(1L);
        mockUser.setTenantId(100L);
        mockUser.setUsername("testuser");
        mockUser.setNickname("Test User");
        mockUser.setMobile("***********");
        mockUser.setEmail("<EMAIL>");
        mockUser.setStatus(1);
        mockUser.setApprovalLevel(2);
        mockUser.setRepairDepotId("DEPOT001");
        mockUser.setAccountType(0);
        mockUser.setCreatedTime(new Date());
        mockUser.setUpdatedTime(new Date());

        // 模拟角色数据
        mockRole = new SysRole();
        mockRole.setId(1L);
        mockRole.setRoleName("测试角色");
        mockRole.setRoleCode("TEST_ROLE");
    }

    @Test
    @DisplayName("创建用户 - 成功场景")
    void createUser_Success() {
        // 设置模拟行为
        when(tableUserService.countByUsername(anyString())).thenReturn(0);
        when(tableUserService.countByMobile(anyString())).thenReturn(0);

        // 模拟insert方法返回带有ID的用户对象
        SysUser insertedUser = new SysUser();
        insertedUser.setId(1L);
        when(tableUserService.insert(any(SysUser.class))).thenReturn(insertedUser);

        // 执行测试
        Long userId = userService.createUser(createDTO);

        // 验证结果
        assertNotNull(userId);
        assertEquals(1L, userId);
        verify(tableUserService).countByUsername("testuser");
        verify(tableUserService).countByMobile("***********");
        verify(tableUserService).insert(any(SysUser.class));
    }

    @Test
    @DisplayName("创建用户 - 用户名已存在")
    void createUser_UsernameExists() {
        // 设置模拟行为
        when(tableUserService.countByUsername(anyString())).thenReturn(1);

        // 执行测试并验证异常
        UserException exception = assertThrows(UserException.class, () -> {
            userService.createUser(createDTO);
        });

        assertEquals(ErrorCode.USER_ALREADY_EXISTS.getCode(), exception.getCode());
        assertEquals("用户名已存在", exception.getMessage());
        verify(tableUserService).countByUsername("testuser");
        verify(tableUserService, never()).insert(any(SysUser.class));
    }

    @Test
    @DisplayName("创建用户 - 手机号已存在")
    void createUser_MobileExists() {
        // 设置模拟行为
        when(tableUserService.countByUsername(anyString())).thenReturn(0);
        when(tableUserService.countByMobile(anyString())).thenReturn(1);

        // 执行测试并验证异常
        UserException exception = assertThrows(UserException.class, () -> {
            userService.createUser(createDTO);
        });

        assertEquals(ErrorCode.USER_ALREADY_EXISTS.getCode(), exception.getCode());
        assertEquals("手机号已存在", exception.getMessage());
        verify(tableUserService).countByUsername("testuser");
        verify(tableUserService).countByMobile("***********");
        verify(tableUserService, never()).insert(any(SysUser.class));
    }

    @Test
    @DisplayName("查询用户列表 - 无条件查询")
    void getUserList_NoConditions() {
        // 设置模拟行为
        when(tableUserService.findByCondition(any(), any(), any(), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<?> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        verify(tableUserService).findByCondition(eq(null), eq(null), eq(null), any());
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 按姓名查询")
    void getUserList_WithNickname() {
        // 设置查询条件
        queryDTO.setNickname("张三");

        // 设置模拟行为
        when(tableUserService.findByCondition(eq("张三"), eq(null), eq(null), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<?> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        verify(tableUserService).findByCondition(eq("张三"), eq(null), eq(null), any());
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 按手机号查询")
    void getUserList_WithMobile() {
        // 设置查询条件
        queryDTO.setMobile("138");

        // 设置模拟行为
        when(tableUserService.findByCondition(eq(null), eq("138"), eq(null), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<?> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        verify(tableUserService).findByCondition(eq(null), eq("138"), eq(null), any());
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 按角色查询")
    void getUserList_WithRoleId() {
        // 设置查询条件
        queryDTO.setRoleId(1L);

        // 设置模拟行为
        when(tableUserService.findByCondition(eq(null), eq(null), eq(1L), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<?> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        verify(tableUserService).findByCondition(eq(null), eq(null), eq(1L), any());
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 组合条件查询")
    void getUserList_WithCombinedConditions() {
        // 设置查询条件
        queryDTO.setNickname("张三");
        queryDTO.setMobile("138");
        queryDTO.setRoleId(1L);

        // 设置模拟行为
        when(tableUserService.findByCondition(eq("张三"), eq("138"), eq(1L), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<?> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        verify(tableUserService).findByCondition(eq("张三"), eq("138"), eq(1L), any());
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 空结果")
    void getUserList_EmptyResult() {
        // 设置模拟行为
        when(tableUserService.findByCondition(any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        BasePageVO<?> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertTrue(result.getList().isEmpty());
        verify(tableUserService).findByCondition(eq(null), eq(null), eq(null), any());
        verify(tableRoleService, never()).findByUserId(anyLong());
    }

    @Test
    @DisplayName("查询用户列表 - 验证租户ID字段映射")
    void getUserList_VerifyTenantIdMapping() {
        // 设置模拟行为
        when(tableUserService.findByCondition(eq(null), eq(null), eq(null), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<UserVO> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());

        UserVO userVO = result.getList().get(0);
        assertNotNull(userVO.getTenantId());
        assertEquals(100L, userVO.getTenantId());
        assertEquals(1L, userVO.getId());
        assertEquals("Test User", userVO.getNickname());

        verify(tableUserService).findByCondition(eq(null), eq(null), eq(null), any());
        verify(tableRoleService).findByUserId(1L);
    }

    @Test
    @DisplayName("查询用户列表 - 验证审批层级字段映射")
    void getUserList_VerifyApprovalLevelMapping() {
        // 设置模拟行为
        when(tableUserService.findByCondition(eq(null), eq(null), eq(null), any()))
            .thenReturn(Arrays.asList(mockUser));
        when(tableRoleService.findByUserId(anyLong()))
            .thenReturn(Arrays.asList(mockRole));

        // 执行测试
        BasePageVO<UserVO> result = userService.getUserList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());

        UserVO userVO = result.getList().get(0);
        assertNotNull(userVO.getApprovalLevel());
        assertEquals(Integer.valueOf(2), userVO.getApprovalLevel());

        verify(tableUserService).findByCondition(eq(null), eq(null), eq(null), any());
        verify(tableRoleService).findByUserId(1L);
    }
}