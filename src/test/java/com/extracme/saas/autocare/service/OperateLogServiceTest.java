package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.model.dto.OperateLogQueryDTO;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.OperateLogVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.util.OperateLogUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 操作日志服务测试类
 */
@SpringBootTest
@ActiveProfiles("local")
@Transactional
class OperateLogServiceTest {

    @Autowired
    private OperateLogService operateLogService;

    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 创建模拟登录用户
        mockLoginUser = new LoginUser();
        SysUser user = new SysUser();
        user.setId(1L);
        user.setUsername("testuser");
        user.setTenantId(1L);
        user.setAccountType(1); // 普通用户
        mockLoginUser.setUser(user);

        // 设置到Session中（这里需要根据实际的SessionUtils实现来调整）
        // SessionUtils.setLoginUser(mockLoginUser);
    }

    @Test
    @DisplayName("测试异步记录操作日志")
    void testRecordOperateLogAsync() {
        // 测试记录用户创建日志
        operateLogService.recordOperateLogAsync(
            OperateLogModuleTypeEnum.USER_MANAGEMENT,
            OperateLogOperateTypeEnum.CREATE,
            "创建用户：测试用户",
            "系统自动记录"
        );

        // 测试记录角色更新日志
        operateLogService.recordOperateLogAsync(
            OperateLogModuleTypeEnum.SYSTEM_MANAGEMENT,
            OperateLogOperateTypeEnum.UPDATE,
            "更新角色：管理员角色"
        );

        // 由于是异步操作，这里只能验证方法调用不抛异常
        // 实际的数据库验证需要在集成测试中进行
        assertTrue(true, "异步记录操作日志方法调用成功");
    }

    @Test
    @DisplayName("测试分页查询操作日志")
    void testGetOperateLogList() {
        // 创建查询参数
        OperateLogQueryDTO queryDTO = new OperateLogQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 执行查询
        BasePageVO<OperateLogVO> result = operateLogService.getOperateLogList(queryDTO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "数据列表不应为空");
        assertTrue(result.getPageNum() >= 1, "页码应大于等于1");
        assertTrue(result.getPageSize() > 0, "页大小应大于0");
    }

    @Test
    @DisplayName("测试枚举类功能")
    void testEnums() {
        // 测试模块类型枚举
        assertEquals("用户管理", OperateLogModuleTypeEnum.USER_MANAGEMENT.getName());
        assertEquals("系统管理", OperateLogModuleTypeEnum.SYSTEM_MANAGEMENT.getName());
        assertEquals(Integer.valueOf(1), OperateLogModuleTypeEnum.USER_MANAGEMENT.getCode());
        assertEquals(Integer.valueOf(2), OperateLogModuleTypeEnum.SYSTEM_MANAGEMENT.getCode());

        // 测试操作类型枚举
        assertEquals("新增", OperateLogOperateTypeEnum.CREATE.getName());
        assertEquals("修改", OperateLogOperateTypeEnum.UPDATE.getName());
        assertEquals("删除", OperateLogOperateTypeEnum.DELETE.getName());
        assertEquals("禁用", OperateLogOperateTypeEnum.DISABLE.getName());
        assertEquals("启用", OperateLogOperateTypeEnum.ENABLE.getName());

        // 测试根据编码获取枚举
        assertEquals(OperateLogModuleTypeEnum.USER_MANAGEMENT,
                    OperateLogModuleTypeEnum.getByCode(1));
        assertEquals(OperateLogOperateTypeEnum.CREATE,
                    OperateLogOperateTypeEnum.getByCode(1));

        // 测试根据编码获取名称
        assertEquals("用户管理", OperateLogModuleTypeEnum.getNameByCode(1));
        assertEquals("新增", OperateLogOperateTypeEnum.getNameByCode(1));
    }

    @Test
    @DisplayName("测试增强的操作日志工具类")
    void testEnhancedOperateLogUtil() {
        // 测试用户创建详细日志
        assertDoesNotThrow(() -> {
            OperateLogUtil.recordUserCreateDetailed("15700091234", "张三");
        });

        // 测试用户删除详细日志
        assertDoesNotThrow(() -> {
            OperateLogUtil.recordUserDeleteDetailed("15700091234", "张三");
        });

        // 测试角色基本信息更新日志
        assertDoesNotThrow(() -> {
            OperateLogUtil.recordRoleInfoUpdateDetailed("旧角色名", "新角色名", "旧描述", "新描述");
        });
    }
}
