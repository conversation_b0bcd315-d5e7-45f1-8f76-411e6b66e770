package com.extracme.saas.autocare.demo;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.service.OperateLogService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 操作日志异步记录演示类
 * 
 * 用于演示修复后的异步操作日志记录功能
 */
@Slf4j
@Component
public class OperateLogAsyncDemo {

    @Autowired
    private OperateLogService operateLogService;

    /**
     * 演示原有方法（可能在异步线程中失败）
     */
    public void demonstrateOldMethod() {
        log.info("=== 演示原有方法 ===");
        
        try {
            // 使用原有的异步方法（依赖Request上下文）
            operateLogService.recordOperateLogAsync(
                OperateLogModuleTypeEnum.USER_MANAGEMENT,
                OperateLogOperateTypeEnum.CREATE,
                "演示原有方法：创建用户",
                "这个方法在异步线程中可能会失败"
            );
            log.info("原有方法调用成功");
        } catch (Exception e) {
            log.error("原有方法调用失败", e);
        }
    }

    /**
     * 演示新的带用户信息参数的方法（在异步线程中也能正常工作）
     */
    public void demonstrateNewMethod() {
        log.info("=== 演示新的带用户信息参数的方法 ===");
        
        try {
            // 使用新的带用户信息参数的异步方法
            operateLogService.recordOperateLogAsync(
                OperateLogModuleTypeEnum.USER_MANAGEMENT,
                OperateLogOperateTypeEnum.CREATE,
                "演示新方法：创建用户",
                "这个方法在异步线程中也能正常工作",
                1L,           // 用户ID
                "demouser",   // 用户名
                1L            // 租户ID
            );
            log.info("新方法调用成功");
        } catch (Exception e) {
            log.error("新方法调用失败", e);
        }
    }

    /**
     * 演示工具类方法（已修复，会自动获取用户信息并传递）
     */
    public void demonstrateUtilMethod() {
        log.info("=== 演示工具类方法 ===");
        
        try {
            // 使用工具类方法（已修复，会自动获取用户信息）
            OperateLogUtil.recordUserLog(
                OperateLogOperateTypeEnum.CREATE,
                "演示工具类方法：创建用户"
            );
            log.info("工具类方法调用成功");
        } catch (Exception e) {
            log.error("工具类方法调用失败", e);
        }
    }

    /**
     * 演示在异步线程中的操作日志记录
     */
    public void demonstrateAsyncThread() {
        log.info("=== 演示在异步线程中的操作日志记录 ===");
        
        // 模拟异步线程
        Thread asyncThread = new Thread(() -> {
            log.info("在异步线程中执行操作日志记录，线程名：{}", Thread.currentThread().getName());
            
            // 在异步线程中，原有方法会失败
            log.info("--- 测试原有方法在异步线程中的表现 ---");
            demonstrateOldMethod();
            
            // 在异步线程中，新方法能正常工作
            log.info("--- 测试新方法在异步线程中的表现 ---");
            demonstrateNewMethod();
            
            // 在异步线程中，工具类方法会降级到原有方法
            log.info("--- 测试工具类方法在异步线程中的表现 ---");
            demonstrateUtilMethod();
        });
        
        asyncThread.setName("demo-async-thread");
        asyncThread.start();
        
        try {
            asyncThread.join(); // 等待异步线程完成
        } catch (InterruptedException e) {
            log.error("等待异步线程完成时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 运行所有演示
     */
    public void runAllDemos() {
        log.info("开始运行操作日志异步记录演示");
        
        // 在主线程中演示
        log.info("=== 在主线程中演示 ===");
        demonstrateOldMethod();
        demonstrateNewMethod();
        demonstrateUtilMethod();
        
        // 在异步线程中演示
        demonstrateAsyncThread();
        
        log.info("操作日志异步记录演示完成");
    }
}
