package com.extracme.saas.autocare.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RoleDTO {
    /**
     * 角色ID（更新时必填）
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    private String roleCode;

    /**
     * 角色类型：1-超级管理员，2-普通管理员，3-普通用户
     */
    private Integer roleType;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态（0-禁用，1-启用）
     */
    @NotNull(message = "角色状态不能为空")
    private Integer status;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;
}