package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "维修厂列表查询DTO")
public class RepairDepotQueryDTO extends BasePageDTO {
    
    @ApiModelProperty(value = "主要合作子公司", example = "000T")
    private String mainOrgId;

    @ApiModelProperty(value = "其他合作子公司", example = "000T")
    private String otherOrgId;

    @ApiModelProperty(value = "修理厂等级", example = "A")
    private String repairDepotGrade;

    @ApiModelProperty(value = "是否保养点(0-否 1-是)", example = "1", required = true)
    private Integer maintenancePoint;

    @ApiModelProperty(value = "修理厂名称", example = "嘉善宝通汽修服务有限公司")
    private String repairDepotName;

    @ApiModelProperty(value = "状态(0-无效 1-有效)")
    private Integer status;

    @ApiModelProperty(value = "合作模式(1-长租 2-分时 3-短租)", required = true)
    private List<Integer> cooperationModeList;

    @ApiModelProperty(value = "修理厂类型(1:合作修理厂 2:非合作修理厂)")
    private Integer repairDepotType;

} 