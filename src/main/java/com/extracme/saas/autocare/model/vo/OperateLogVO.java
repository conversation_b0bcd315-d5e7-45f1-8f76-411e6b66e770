package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 操作日志VO
 */
@Data
@ApiModel(description = "操作日志信息")
public class OperateLogVO {

    @ApiModelProperty(value = "日志ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;

    @ApiModelProperty(value = "租户名称", example = "测试租户")
    private String tenantName;

    @ApiModelProperty(value = "模块类型", example = "1")
    private Integer moduleType;

    @ApiModelProperty(value = "模块类型名称", example = "用户管理")
    private String moduleTypeName;

    @ApiModelProperty(value = "操作类型", example = "1")
    private Integer operateType;

    @ApiModelProperty(value = "操作类型名称", example = "新增")
    private String operateTypeName;

    @ApiModelProperty(value = "操作内容描述", example = "创建用户：张三")
    private String content;

    @ApiModelProperty(value = "备注", example = "系统自动记录")
    private String miscDesc;

    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @ApiModelProperty(value = "修改时间", example = "2024-01-01 12:00:00")
    private Date updateTime;

    @ApiModelProperty(value = "修改人", example = "admin")
    private String updateBy;
}
