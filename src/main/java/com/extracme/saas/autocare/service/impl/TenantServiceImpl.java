package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TenantDTO;
import com.extracme.saas.autocare.model.dto.TenantQueryDTO;
import com.extracme.saas.autocare.model.dto.TenantUpdateDTO;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.vo.TenantVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.service.TenantService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantServiceImpl implements TenantService {

    private final TableTenantService tableTenantService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTenant(TenantDTO tenantDTO) {
        // 检查租户编码是否已存在
        if (tableTenantService.existsByTenantCode(tenantDTO.getTenantCode())) {
            throw new BusinessException(ErrorCode.TENANT_CODE_EXISTS);
        }

        // 创建租户实体
        SysTenant tenant = new SysTenant();
        BeanUtils.copyProperties(tenantDTO, tenant);

        // 设置默认值
        if (tenant.getStatus() == null) {
            tenant.setStatus(1); // 默认启用
        }

        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        // 保存租户
        SysTenant savedTenant = tableTenantService.insert(tenant, operator);
        return savedTenant.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenant(TenantUpdateDTO tenantUpdateDTO) {
        // 检查租户是否存在
        SysTenant existingTenant = tableTenantService.selectById(tenantUpdateDTO.getId());
        if (existingTenant == null) {
            throw new BusinessException(ErrorCode.TENANT_NOT_FOUND);
        }

        // 检查租户编码是否已被其他租户使用
        if (!existingTenant.getTenantCode().equals(tenantUpdateDTO.getTenantCode())) {
            if (tableTenantService.existsByTenantCode(tenantUpdateDTO.getTenantCode())) {
                throw new BusinessException(ErrorCode.TENANT_CODE_EXISTS);
            }
        }

        // 更新租户
        SysTenant tenant = new SysTenant();
        BeanUtils.copyProperties(tenantUpdateDTO, tenant);
        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        tableTenantService.updateSelectiveById(tenant, operator);
    }

    @Override
    public TenantVO getTenant(Long id) {
        SysTenant tenant = tableTenantService.selectById(id);
        if (tenant == null) {
            return null;
        }

        TenantVO tenantVO = new TenantVO();
        BeanUtils.copyProperties(tenant, tenantVO);
        return tenantVO;
    }

    @Override
    public BasePageVO<TenantVO> getTenantList(TenantQueryDTO queryDTO) {
        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询租户列表
        List<SysTenant> tenants = tableTenantService.findByCondition(
            queryDTO.getTenantName(),
            queryDTO.getTenantCode(),
            queryDTO.getStatus()
        );

        // 转换为VO
        List<TenantVO> tenantVOs = tenants.stream().map(tenant -> {
            TenantVO tenantVO = new TenantVO();
            BeanUtils.copyProperties(tenant, tenantVO);
            return tenantVO;
        }).collect(Collectors.toList());

        // 构建分页结果
        PageInfo<SysTenant> pageInfo = new PageInfo<>(tenants);
        return BasePageVO.of(tenantVOs, pageInfo);
    }

    @Override
    public List<ComboVO<Long>> getTenantCombo() {
        try {
            // 查询所有有效的租户
            List<SysTenant> tenants = tableTenantService.findByCondition(null, null, 1);

            // 转换为ComboVO<Long>
            List<ComboVO<Long>> result = tenants.stream()
                .map(tenant -> {
                    ComboVO<Long> vo = new ComboVO<>();
                    vo.setId(tenant.getId());
                    vo.setValue(tenant.getTenantName());
                    vo.setRemark(tenant.getTenantCode()); // 将租户编码存储到remark字段
                    return vo;
                })
                .collect(Collectors.toList());
            return result;
        } catch (Exception e) {
            log.error("获取租户下拉列表数据失败", e);
            throw new RuntimeException("获取租户下拉列表失败", e);
        }
    }
}
