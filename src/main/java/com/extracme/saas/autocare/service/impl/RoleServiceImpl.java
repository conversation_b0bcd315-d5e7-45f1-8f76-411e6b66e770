package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysRolePermission;

import com.extracme.saas.autocare.model.vo.PermissionTreeVO;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.service.RoleService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final TableRoleService tableRoleService;
    private final TablePermissionService tablePermissionService;
    private final PermissionService permissionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRole(SysRole role) {
        // 根据用户类型设置租户ID
        if (SessionUtils.isSuperAdmin()) {
            // 超级管理员创建的角色设置为系统级角色
            role.setTenantId(-1L);
        } else {
            // 普通用户创建的角色设置为当前用户的租户ID
            Long currentTenantId = SessionUtils.getTenantId();
            role.setTenantId(currentTenantId);
        }

        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        SysRole savedRole = tableRoleService.insert(role, operator);

        // 记录操作日志
        OperateLogUtil.recordRoleCreate(role.getRoleName());

        return savedRole.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(SysRole role) {
        // 获取更新前的角色信息用于对比
        SysRole oldRole = tableRoleService.selectById(role.getId());
        if (oldRole == null) {
            log.warn("更新角色失败，角色不存在，ID: {}", role.getId());
            return;
        }

        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        tableRoleService.updateSelectiveById(role, operator);

        // 记录角色基本信息更新日志
        recordRoleInfoUpdateLog(oldRole, role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        // 先查询角色信息，用于记录日志
        SysRole existingRole = tableRoleService.selectById(id);
        String roleName = existingRole != null ? existingRole.getRoleName() : "未知角色";

        // 设置删除标记和更新时间
        SysRole role = new SysRole();
        role.setId(id);
        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        tableRoleService.updateSelectiveById(role, operator);

        // 删除角色权限关联
        tablePermissionService.deleteByRoleId(id);

        // 记录操作日志
        OperateLogUtil.recordRoleDelete(roleName);
    }

    @Override
    public Optional<SysRole> findById(Long id) {
        return Optional.ofNullable(tableRoleService.selectById(id));
    }

    @Override
    public List<SysRole> findByRoleNameLike(String roleName) {
        // 调用已应用多租户访问控制的Repository方法
        return tableRoleService.findByCondition(roleName, null);
    }

    @Override
    public List<SysRole> findAll() {
        // 调用已应用多租户访问控制的Repository方法
        return tableRoleService.findAll();
    }

    @Override
    public RoleVO getRole(Long roleId) {
        SysRole role = tableRoleService.selectById(roleId);
        if (role == null) {
            return null;
        }
        RoleVO roleVO = new RoleVO();
        BeanUtils.copyProperties(role, roleVO);

        // 获取角色权限ID列表
        List<Long> permissionIds = tablePermissionService.findPermissionIdsByRoleId(roleId);
        roleVO.setPermissionIds(permissionIds);

        // 获取权限树并设置到角色VO中
        List<PermissionTreeVO> allPermissionTree = permissionService.getPermissionTree();

        // 过滤权限树，只保留角色拥有的权限
        List<PermissionTreeVO> rolePermissionTree = filterPermissionTree(allPermissionTree, permissionIds);
        roleVO.setPermissionTree(rolePermissionTree);

        return roleVO;
    }

    /**
     * 过滤权限树，只保留角色拥有的权限
     *
     * @param permissionTree 完整权限树
     * @param permissionIds 角色拥有的权限ID列表
     * @return 过滤后的权限树
     */
    private List<PermissionTreeVO> filterPermissionTree(List<PermissionTreeVO> permissionTree, List<Long> permissionIds) {
        if (permissionTree == null || permissionTree.isEmpty()) {
            return new ArrayList<>();
        }

        // 深拷贝权限树，避免修改原始数据
        List<PermissionTreeVO> result = new ArrayList<>();

        for (PermissionTreeVO node : permissionTree) {
            // 如果当前节点在角色权限列表中，则添加到结果中
            if (permissionIds.contains(node.getId())) {
                PermissionTreeVO newNode = new PermissionTreeVO();
                BeanUtils.copyProperties(node, newNode);

                // 递归处理子节点
                if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                    newNode.setChildren(filterPermissionTree(node.getChildren(), permissionIds));
                }

                result.add(newNode);
            }
        }

        return result;
    }

    @Override
    public BasePageVO<RoleVO> getRoleList(RoleQueryDTO queryDTO) {
        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 使用统一的查询方法，支持角色名称模糊查询和多租户访问控制
        // findByCondition方法内部已经实现了多租户访问控制逻辑
        List<SysRole> roles = tableRoleService.findByCondition(queryDTO.getRoleName(), queryDTO.getTenantId());

        List<RoleVO> roleVOs = roles.stream().map(role -> {
            RoleVO roleVO = new RoleVO();
            BeanUtils.copyProperties(role, roleVO);
            roleVO.setPermissionIds(tablePermissionService.findPermissionIdsByRoleId(role.getId()));
            return roleVO;
        }).collect(Collectors.toList());

        PageInfo<SysRole> pageInfo = new PageInfo<>(roles);
        return BasePageVO.of(roleVOs, pageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignPermissions(Long roleId, List<Long> permissionIds) {
        // 获取角色信息用于日志记录
        SysRole role = tableRoleService.selectById(roleId);
        String roleName = role != null ? role.getRoleName() : "未知角色";

        // 获取原有权限列表用于对比
        List<Long> oldPermissionIds = tablePermissionService.findPermissionIdsByRoleId(roleId);

        // 删除原有权限
        tablePermissionService.deleteByRoleId(roleId);

        // 批量插入新权限
        if (permissionIds != null && !permissionIds.isEmpty()) {
            // 获取当前登录用户作为操作人
            String operator = SessionUtils.getUsername();
            Date now = new Date();

            List<SysRolePermission> rolePermissions = permissionIds.stream()
                .map(permissionId -> {
                    SysRolePermission rp = new SysRolePermission();
                    rp.setRoleId(roleId);
                    rp.setPermissionId(permissionId);
                    rp.setCreatedTime(now);
                    rp.setCreateBy(operator);
                    return rp;
                })
                .collect(Collectors.toList());
            tablePermissionService.batchCreate(rolePermissions);
        }

        // 记录权限变更日志
        recordPermissionChangeLog(roleName, oldPermissionIds, permissionIds);
    }

    @Override
    public List<Long> findPermissionIdsByRoleId(Long roleId) {
        return tablePermissionService.findPermissionIdsByRoleId(roleId);
    }

    @Override
    public List<Long> findPermissionIdsByRoleIds(List<Long> roleIds) {
        return tablePermissionService.findPermissionIdsByRoleIds(roleIds);
    }

    @Override
    public List<ComboVO<String>> getRoleCombo() {
        // 查询所有有效的角色（已应用多租户访问控制）
        List<SysRole> roles = tableRoleService.findAll().stream()
            .filter(role -> role.getStatus() != null && role.getStatus() == 1)
            .collect(Collectors.toList());

        // 转换为ComboVO<String>
        return roles.stream().map(role -> {
            ComboVO<String> vo = new ComboVO<>();
            vo.setId(String.valueOf(role.getId()));
            vo.setValue(role.getRoleName());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 记录权限变更日志
     *
     * @param roleName 角色名称
     * @param oldPermissionIds 原有权限ID列表
     * @param newPermissionIds 新权限ID列表
     */
    private void recordPermissionChangeLog(String roleName, List<Long> oldPermissionIds, List<Long> newPermissionIds) {
        try {
            // 处理空列表
            List<Long> oldIds = oldPermissionIds != null ? oldPermissionIds : Collections.emptyList();
            List<Long> newIds = newPermissionIds != null ? newPermissionIds : Collections.emptyList();

            // 计算新增和删除的权限ID
            List<Long> addedIds = newIds.stream()
                .filter(id -> !oldIds.contains(id))
                .collect(Collectors.toList());

            List<Long> removedIds = oldIds.stream()
                .filter(id -> !newIds.contains(id))
                .collect(Collectors.toList());

            // 如果没有变更，不记录日志
            if (addedIds.isEmpty() && removedIds.isEmpty()) {
                return;
            }

            // 查询权限名称
            List<SysPermission> addedPermissions = getPermissionsByIds(addedIds);
            List<SysPermission> removedPermissions = getPermissionsByIds(removedIds);

            // 记录详细的权限变更日志
            OperateLogUtil.recordPermissionAssignDetailed(roleName, addedPermissions, removedPermissions);

        } catch (Exception e) {
            log.error("记录权限变更日志失败，角色: {}", roleName, e);
        }
    }

    /**
     * 根据权限ID列表查询权限信息
     *
     * @param permissionIds 权限ID列表
     * @return 权限信息列表
     */
    private List<SysPermission> getPermissionsByIds(List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 批量查询权限信息
            return permissionIds.stream()
                .map(tablePermissionService::selectById)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询权限信息失败，权限ID列表: {}", permissionIds, e);
            return Collections.emptyList();
        }
    }

    /**
     * 记录角色基本信息更新日志
     *
     * @param oldRole 更新前的角色信息
     * @param newRole 更新后的角色信息
     */
    private void recordRoleInfoUpdateLog(SysRole oldRole, SysRole newRole) {
        try {
            // 只记录角色名称和描述的变更
            String oldRoleName = oldRole.getRoleName();
            String newRoleName = newRole.getRoleName() != null ? newRole.getRoleName() : oldRole.getRoleName();
            String oldDescription = oldRole.getDescription();
            String newDescription = newRole.getDescription() != null ? newRole.getDescription() : oldRole.getDescription();

            // 记录角色基本信息更新日志
            OperateLogUtil.recordRoleInfoUpdateDetailed(oldRoleName, newRoleName, oldDescription, newDescription);

        } catch (Exception e) {
            log.error("记录角色基本信息更新日志失败，角色ID: {}", newRole.getId(), e);
        }
    }
}