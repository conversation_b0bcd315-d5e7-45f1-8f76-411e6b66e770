package com.extracme.saas.autocare.service;

import java.util.List;
import java.util.Optional;

import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

/**
 * 角色服务接口
 */
public interface RoleService {
    /**
     * 根据ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    Optional<SysRole> findById(Long id);

    /**
     * 根据角色名称模糊查询角色列表
     *
     * @param roleName 角色名称
     * @return 角色列表
     */
    List<SysRole> findByRoleNameLike(String roleName);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<SysRole> findAll();

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 角色ID
     */
    Long createRole(SysRole role);

    /**
     * 更新角色
     *
     * @param role 角色信息
     */
    void updateRole(SysRole role);

    /**
     * 删除角色
     *
     * @param id 角色ID
     */
    void deleteRole(Long id);

    /**
     * 获取角色详情
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    RoleVO getRole(Long roleId);

    /**
     * 分页查询角色列表
     *
     * @param queryDTO 查询条件
     * @return 角色列表
     */
    BasePageVO<RoleVO> getRoleList(RoleQueryDTO queryDTO);

    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void assignPermissions(Long roleId, List<Long> permissionIds);

    /**
     * 获取角色权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> findPermissionIdsByRoleId(Long roleId);

    /**
     * 获取角色权限ID列表
     *
     * @param roleIds 角色ID列表
     * @return 权限ID列表
     */
    List<Long> findPermissionIdsByRoleIds(List<Long> roleIds);

    /**
     * 获取角色下拉列表
     *
     * @return 角色下拉列表，ID类型为String
     */
    List<ComboVO<String>> getRoleCombo();
}