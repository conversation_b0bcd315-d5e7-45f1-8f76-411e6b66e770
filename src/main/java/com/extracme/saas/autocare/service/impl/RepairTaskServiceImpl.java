package com.extracme.saas.autocare.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.AcceptanceInfoDTO;
import com.extracme.saas.autocare.model.dto.AccidentInfoDTO;
import com.extracme.saas.autocare.model.dto.CustomerPaymentDTO;
import com.extracme.saas.autocare.model.dto.QuotationSummaryDTO;
import com.extracme.saas.autocare.model.dto.RepairInfoDTO;
import com.extracme.saas.autocare.model.dto.RepairRemarkDTO;
import com.extracme.saas.autocare.model.dto.VehicleInfoDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskCreateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import com.extracme.saas.autocare.model.entity.ActivityStatus;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairRemark;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.entity.MtcVehicleRepairPic;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.MtcTaskListResultVO;
import com.extracme.saas.autocare.model.vo.RepairTaskCreateInfoVO;
import com.extracme.saas.autocare.model.vo.RepairTaskDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.repository.TableActivityStatusService;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRepairRemarkService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import com.extracme.saas.autocare.repository.TableVehicleRepairPicService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.VehicleInfoService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.RepairPicUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 维修任务服务实现类
 */
@Slf4j
@Service
public class RepairTaskServiceImpl implements RepairTaskService {

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableVehicleRepairPicService tableVehicleRepairPicService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableActivityDefinitionService tableActivityDefinitionService;

    @Autowired
    private TableActivityStatusService tableActivityStatusService;

    @Autowired
    private TableActivityInstanceService tableActivityInstanceService;

    @Autowired
    private TableRepairDepotInfoService tableRepairDepotInfoService;

    @Autowired
    private VehicleInfoService vehicleInfoService;

    @Autowired
    private TableVehicleModelService tableVehicleModelService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableRepairRemarkService tableRepairRemarkService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRepairTask(RepairTaskCreateDTO createDTO) {
        log.info("创建维修任务，参数: {}", createDTO);
        try {
            // 1. 参数校验
            validateRepairTaskParams(createDTO);

            // 2. 创建维修任务
            MtcRepairTask newRepairTask = new MtcRepairTask();

            // 3. 从车辆表获取车辆信息
            enrichVehicleInfo(newRepairTask, createDTO.getVin(), createDTO.getVehicleNo());

            // 4. 从修理厂表获取修理厂信息
            enrichRepairDepotInfo(newRepairTask, createDTO.getRepairDepotId());

            // 5. 复制DTO中的字段到实体类
            BeanUtils.copyProperties(createDTO, newRepairTask);

            // 6. 设置维修类型名称
            setRepairTypeName(newRepairTask);

            // 7. 生成任务编号 根据维修类型生成不同前缀的任务编号
            String prefix = getTaskNoPrefix(createDTO.getRepairTypeId());
            newRepairTask.setTaskNo(prefix + System.currentTimeMillis());
            // 设置任务类型为4（saas维修任务）
            newRepairTask.setTaskType((short) 4);

            // 8. 设置任务创建时间
            newRepairTask.setTaskCreateTime(new Date());
            newRepairTask.setTaskInflowTime(new Date());

            // 9. 保存维修任务
            tableRepairTaskService.insert(newRepairTask, SessionUtils.getUsername());
            Long repairTaskId = newRepairTask.getId();

            // 10. 处理维修图片信息
            Map<BigDecimal, List<String>> mediaTypeMap = new HashMap<>();
            mediaTypeMap.put(RepairPicTypeEnum.CREATE_PICTURE.getTypeId(), createDTO.getCreatePicList()); // 创建图片
            processMediaFiles(newRepairTask.getTaskNo(), mediaTypeMap, SessionUtils.getUsername());

            // 11. 启动工作流 - 自动匹配合适的工作流模板
            startWorkflowForRepairTask(newRepairTask);

            // 12. 记录操作日志
            saveOperationLogs(newRepairTask);

            // 13. 处理其他业务逻辑
            processAdditionalLogic(newRepairTask);

            log.info("维修任务创建成功，ID: {}", repairTaskId);
            return repairTaskId;
        } catch (RuntimeException e) {
            log.error("创建维修任务失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建维修任务失败", e);
            throw new RuntimeException("创建维修任务失败: " + e.getMessage());
        }
    }

    /**
     * 从车辆表获取车辆信息并填充到维修任务中
     *
     * @param repairTask 维修任务
     * @param vin        车架号
     * @param vehicleNo  车牌号
     */
    private void enrichVehicleInfo(MtcRepairTask repairTask, String vin, String vehicleNo) {
        // 根据车架号获取车辆信息
        MtcVehicleInfo vehicleInfo = vehicleInfoService.getVehicleInfoByVin(vin);
        if (vehicleInfo == null) {
            throw new BusinessException("未找到对应的车辆信息");
        }
        repairTask.setVehicleModelSeq(vehicleInfo.getVehicleModelId());
        repairTask.setOrgId(vehicleInfo.getVehicleOrgId());
        repairTask.setOperateOrgId(vehicleInfo.getOperationOrgId());
        repairTask.setProductLine(Integer.valueOf(vehicleInfo.getProductLine()));
        repairTask.setSubProductLine(Integer.valueOf(vehicleInfo.getSubProductLine()));
        repairTask.setFactOperateTag(Integer.valueOf(vehicleInfo.getFactOperateTag()));

        repairTask.setInsuranceCompanyName("不知从何而来");

        // 根据车型ID获取车型信息
        MtcVehicleModel vehicleModel = tableVehicleModelService.selectById(vehicleInfo.getVehicleModelId());
        if (vehicleModel != null) {
            repairTask.setVehicleModelInfo(vehicleModel.getVehicleModelName());
        }

        // 根据组织机构ID获取组织机构信息
        MtcOrgInfo orgInfo = tableOrgInfoService.selectByOrgId(vehicleInfo.getVehicleOrgId());
        if (orgInfo != null) {
            repairTask.setOrgName(orgInfo.getOrgName());
        }

        // 根据组织机构ID获取组织机构信息
        MtcOrgInfo operateOrgInfo = tableOrgInfoService.selectByOrgId(vehicleInfo.getOperationOrgId());
        if (operateOrgInfo != null) {
            repairTask.setOperateOrgName(operateOrgInfo.getOrgName());
        }
    }

    /**
     * 从修理厂表获取修理厂信息并填充到维修任务中
     *
     * @param repairTask    维修任务
     * @param repairDepotId 修理厂ID
     */
    private void enrichRepairDepotInfo(MtcRepairTask repairTask, String repairDepotId) {
        // 根据修理厂ID获取修理厂信息
        MtcRepairDepotInfo repairDepotInfo = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);
        if (repairDepotInfo == null) {
            throw new BusinessException("未找到对应的修理厂");
        }

        try {
            BeanUtils.copyProperties(repairDepotInfo, repairTask);
            repairTask.setRepairGrade(repairDepotInfo.getRepairDepotGrade());
        } catch (Exception e) {
            log.warn("获取修理厂信息失败: {}", e.getMessage());
        }
    }

    /**
     * 设置维修类型名称
     *
     * @param repairTask 维修任务
     */
    private void setRepairTypeName(MtcRepairTask repairTask) {
        // 根据维修类型ID设置维修类型名称
        Integer repairTypeId = repairTask.getRepairTypeId();
        if (repairTypeId != null) {
            switch (repairTypeId) {
                case 1:
                    repairTask.setRepairTypeName("事故维修");
                    break;
                case 2:
                    repairTask.setRepairTypeName("自费维修");
                    break;
                case 3:
                    repairTask.setRepairTypeName("车辆保养");
                    break;
                case 4:
                    repairTask.setRepairTypeName("轮胎任务");
                    break;
                case 6:
                    repairTask.setRepairTypeName("常规保养");
                    break;
                default:
                    repairTask.setRepairTypeName("其他维修");
            }
        }
    }

    /**
     * 验证维修任务参数
     *
     * @param createDTO 创建参数
     */
    private void validateRepairTaskParams(RepairTaskCreateDTO createDTO) {
        if (createDTO == null) {
            throw new RuntimeException("创建参数不能为空");
        }

        if (StringUtils.isBlank(createDTO.getVin())) {
            throw new RuntimeException("车架号不能为空");
        }

        if (StringUtils.isBlank(createDTO.getVehicleNo())) {
            throw new RuntimeException("车牌号不能为空");
        }

        if (createDTO.getRepairTypeId() == null) {
            throw new RuntimeException("维修类型不能为空");
        }
    }

    /**
     * 根据维修类型获取任务编号前缀
     *
     * @param repairTypeId 维修类型ID
     * @return 任务编号前缀
     */
    private String getTaskNoPrefix(Integer repairTypeId) {
        // 根据维修类型返回不同的前缀
        switch (repairTypeId) {
            case 1: // 事故维修
                return "SG";
            case 2: // 自费维修
                return "ZF";
            case 3: // 车辆保养
                return "BY";
            case 6: // 常规保养
                return "CG";
            default:
                return "WX";
        }
    }

    /**
     * 根据活动编码设置活动名称
     *
     * @param item 维修任务流程列表视图对象
     * @param activityDefinitions 活动定义列表
     */
    private void setActivityNameByCode(RepairTaskProcessListVO item, List<ActivityDefinition> activityDefinitions) {
        if (item == null || item.getCurrentActivityCode() == null || activityDefinitions == null) {
            return;
        }

        // 根据活动编码查找对应的活动定义
        activityDefinitions.stream()
                .filter(definition -> item.getCurrentActivityCode().equals(definition.getActivityCode()))
                .findFirst()
                .ifPresent(definition -> item.setCurrentActivityName(definition.getActivityName()));
    }

    /**
     * 根据状态编码设置状态名称
     *
     * @param item 维修任务流程列表视图对象
     * @param activityStatuses 活动状态列表
     */
    private void setStatusNameByCode(RepairTaskProcessListVO item, List<ActivityStatus> activityStatuses) {
        if (item == null || item.getStatusCode() == null || activityStatuses == null) {
            return;
        }

        // 根据状态编码查找对应的状态定义
        activityStatuses.stream()
                .filter(status -> item.getStatusCode().equals(status.getStatusCode()))
                .findFirst()
                .ifPresent(status -> item.setStatusName(status.getStatusName()));
    }

    /**
     * 为维修任务启动工作流
     *
     * @param repairTask 维修任务
     */
    private void startWorkflowForRepairTask(MtcRepairTask repairTask) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = SessionUtils.getLoginUser();
            String username = SessionUtils.getUsername();
            Long tenantId = SessionUtils.getTenantId();

            log.info("启动工作流 - 当前用户: {}, 租户ID: {}", username, tenantId);

            // 构建工作流启动参数
            WorkflowStartDTO startDTO = new WorkflowStartDTO();
            startDTO.setBusinessId(repairTask.getTaskNo());
            startDTO.setTaskType(repairTask.getRepairTypeId());

            // 设置修理厂类型
            // 根据修理厂ID判断是合作还是非合作修理厂
            Integer repairFactoryType = determineRepairFactoryType(repairTask.getRepairDepotId());
            startDTO.setRepairFactoryType(repairFactoryType);

            // 设置子产品线
            // 根据车辆业务状态或产品线确定子产品线
            Integer subProductLine = determineSubProductLine(repairTask.getSubProductLine());
            startDTO.setSubProductLine(subProductLine);

            // 设置操作人
            startDTO.setOperator(username);
            startDTO.setRemarks("创建维修任务自动启动工作流");

            // 如果用户有特定的配置信息，可以在这里设置
            if (loginUser != null && loginUser.getUser() != null) {
                // 可以根据用户的角色或权限设置不同的工作流参数
                // 例如：根据用户所属部门设置不同的审批流程

                // 获取用户权限信息
                Set<String> permissions = loginUser.getPermissions();
                if (permissions != null && !permissions.isEmpty()) {
                    log.info("用户权限信息: {}", permissions);
                }

                // 这里可以添加基于用户配置的自定义逻辑
                // 例如：根据用户权限设置不同的工作流参数
            }

            // 启动工作流 - WorkflowService.startWorkflow 方法会自动匹配合适的工作流模板
            Long workflowInstanceId = workflowService.startWorkflow(startDTO);
            log.info("为维修任务[{}]启动工作流成功，工作流实例ID: {}, 租户ID: {}",
                    repairTask.getTaskNo(), workflowInstanceId, tenantId);
        } catch (Exception e) {
            log.error("启动工作流失败: {}", e.getMessage(), e);
            throw new RuntimeException("启动工作流失败: " + e.getMessage());
        }
    }

    /**
     * 确定修理厂类型
     *
     * @param repairDepotId 修理厂ID
     * @return 修理厂类型（1:合作, 2:非合作）
     */
    private Integer determineRepairFactoryType(String repairDepotId) {
        // TODO: 根据修理厂ID查询修理厂信息，确定是合作还是非合作修理厂
        // 这里暂时使用默认值
        return 1; // 默认为合作修理厂
    }

    /**
     * 确定子产品线
     *
     * @param rentType 车辆业务状态
     * @return 子产品线
     */
    private Integer determineSubProductLine(Integer rentType) {
        // TODO: 根据车辆业务状态或其他信息确定子产品线
        // 这里暂时使用默认值
        return 1; // 默认子产品线
    }

    /**
     * 保存操作日志
     *
     * @param repairTask 维修任务
     */
    private void saveOperationLogs(MtcRepairTask repairTask) {
        Date now = new Date();
        String operator = SessionUtils.getUsername();

        // 新增任务日志        
        MtcOperatorLog taskLog = new MtcOperatorLog();
        taskLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        taskLog.setRecordId(repairTask.getId());
        taskLog.setOpeContent("新增维修任务");
        taskLog.setRemark(StringUtils.EMPTY);
        taskLog.setCreatedTime(now);
        taskLog.setCreateBy(operator);
        tableOperatorLogService.insertSelective(taskLog);

        // 流程日志
        MtcOperatorLog processLog = new MtcOperatorLog();
        processLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
        processLog.setRecordId(repairTask.getId());
        processLog.setOpeContent("待处理");
        processLog.setRemark("车辆交接");
        processLog.setCurrentActivityCode(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());
        processLog.setCreatedTime(now);
        processLog.setCreateBy(
                StringUtils.isNotBlank(repairTask.getRepairDepotName()) ? repairTask.getRepairDepotName() : operator);
        tableOperatorLogService.insertSelective(processLog);
    }

    /**
     * 处理额外的业务逻辑
     *
     * @param repairTask 维修任务
     */
    private void processAdditionalLogic(MtcRepairTask repairTask) {
        // TODO: 处理车辆信息更新
        // 例如：更新车辆里程数

        // TODO: 处理关联订单信息
        // 例如：如果有关联订单，更新订单状态

        // TODO: 处理事故关联信息
        // 例如：如果是事故维修，关联事故信息

        // TODO: 处理送修工单创建
        // 例如：创建送修工单

        // TODO: 处理替代车逻辑
        // 例如：如果需要替代车，创建替代车任务
    }

    @Override
    public BasePageVO<RepairTaskProcessListVO> queryRepairTaskProcessList(RepairTaskProcessQueryDTO queryDTO) {
        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询数据
        List<RepairTaskProcessListVO> list = tableRepairTaskService.queryProcessList(queryDTO);
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询所有活动节点定义
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            // 查询所有活动状态定义
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();
            list.forEach(
                    item -> {
                        // 设置活动节点名称
                        setActivityNameByCode(item, activityDefinitions);
                        // 设置活动状态名称
                        setStatusNameByCode(item, activityStatuses);
                    });
        }
        PageInfo<RepairTaskProcessListVO> pageInfo = new PageInfo<>(list);

        // 构建返回结果
        return BasePageVO.of(list, pageInfo);
    }

    @Override
    public RepairTaskDetailsVO getRepairTaskDetails(Long id) {
        log.info("查询维修任务详情，ID: {}", id);
        
        // 查询维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            log.error("未找到对应的维修任务，ID: {}", id);
            throw new BusinessException("未找到对应的维修任务");
        }

        // 查询工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
        if (workflowInstance == null) {
            log.error("未找到对应的工作流实例，任务编号: {}", repairTask.getTaskNo());
            throw new BusinessException("未找到对应的工作流实例");
        }

        // 转换为VO对象
        RepairTaskDetailsVO detailsVO = new RepairTaskDetailsVO();
        BeanUtils.copyProperties(repairTask, detailsVO);
        detailsVO.setCurrentActivityCode(workflowInstance.getCurrentActivityCode());
        detailsVO.setStatusCode(workflowInstance.getStatusCode());
        
        try {
            // 1. 填充车辆信息
            fillVehicleInfo(repairTask, detailsVO);
            
            // 2. 填充创建信息
            fillCreateInfo(repairTask, detailsVO);
            
            // 3. 填充维修信息
            fillRepairInfo(repairTask, detailsVO);
            
            // 4. 填充事故信息
            fillAccidentInfo(repairTask, detailsVO);
            
            // 5. 填充报价合计
            fillQuotationSummary(repairTask, detailsVO);
            
            // 6. 填充客户付款信息
            fillCustomerPayment(repairTask, detailsVO);
            
            // 7. 填充验收信息
            fillAcceptanceInfo(repairTask, detailsVO);
            
            // 8. 填充维修项目明细
            fillRepairItemDetails(repairTask, detailsVO);
            
            // 9. 填充维修图片明细
            fillRepairPictures(repairTask, detailsVO);
            
            log.info("维修任务详情查询成功，ID: {}, 任务编号: {}", id, repairTask.getTaskNo());
        } catch (Exception e) {
            log.error("填充维修任务详情子对象时发生错误: {}", e.getMessage(), e);
            // 不抛出异常，返回已填充的部分数据
        }

        // 返回详情
        return detailsVO;
    }
    
    /**
     * 填充车辆信息
     */
    private void fillVehicleInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        VehicleInfoDTO vehicleInfo = new VehicleInfoDTO();
        vehicleInfo.setVin(repairTask.getVin());
        vehicleInfo.setLicensePlate(repairTask.getVehicleNo());
        vehicleInfo.setVehicleModelId(repairTask.getVehicleModelSeq());
        vehicleInfo.setVehicleModelName(repairTask.getVehicleModelInfo());
        vehicleInfo.setOrgId(repairTask.getOrgId());
        vehicleInfo.setOrgName(repairTask.getOrgName());
        vehicleInfo.setOperateOrgId(repairTask.getOperateOrgId());
        vehicleInfo.setOperateOrgName(repairTask.getOperateOrgName());
        vehicleInfo.setFactOperateTag(repairTask.getFactOperateTag());
        vehicleInfo.setProductLine(repairTask.getProductLine());
        vehicleInfo.setSubProductLine(repairTask.getSubProductLine());
        
        // 查询车辆当前里程数
        try {
            // 这里可以调用车辆服务查询最新里程数
            // vehicleInfo.setCurrentMileage(vehicleInfoService.getVehicleMileage(repairTask.getVin()));
        } catch (Exception e) {
            log.warn("获取车辆里程数失败: {}", e.getMessage());
        }
        
        detailsVO.setVehicleInfo(vehicleInfo);
    }
    
    /**
     * 填充创建信息
     */
    private void fillCreateInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        RepairTaskCreateInfoVO createInfo = new RepairTaskCreateInfoVO();
        BeanUtils.copyProperties(repairTask, createInfo); // 复制基本属性
        
        detailsVO.setCreateInfo(createInfo);
    }
    
    /**
     * 填充维修信息
     */
    private void fillRepairInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        RepairInfoDTO repairInfo = new RepairInfoDTO();
        BeanUtils.copyProperties(repairTask, repairInfo); // 复制基本属性
        
        detailsVO.setRepairInfo(repairInfo);
    }
    
    /**
     * 填充事故信息
     */
    private void fillAccidentInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        // 只有事故维修类型才需要填充事故信息
        if (repairTask.getRepairTypeId() != null && repairTask.getRepairTypeId() == 1) {
            AccidentInfoDTO accidentInfo = new AccidentInfoDTO();
            BeanUtils.copyProperties(repairTask, accidentInfo); // 复制基本属性
            
            // 可以调用事故服务查询更多事故信息
            // 例如：事故状态、责任类型等
            
            detailsVO.setAccidentInfo(accidentInfo);
        }
    }
    
    /**
     * 填充报价合计
     */
    private void fillQuotationSummary(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        QuotationSummaryDTO quotationSummary = new QuotationSummaryDTO();
        BeanUtils.copyProperties(repairTask, quotationSummary); // 复制基本属性
        
        detailsVO.setQuotationSummary(quotationSummary);
    }
    
    /**
     * 填充客户付款信息
     */
    private void fillCustomerPayment(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        CustomerPaymentDTO customerPayment = new CustomerPaymentDTO();
        BeanUtils.copyProperties(repairTask, customerPayment); // 复制基本属性
        
        detailsVO.setCustomerPayment(customerPayment);
    }
    
    /**
     * 填充验收信息
     */
    private void fillAcceptanceInfo(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        AcceptanceInfoDTO acceptanceInfo = new AcceptanceInfoDTO();
        BeanUtils.copyProperties(repairTask, acceptanceInfo); // 复制基本属性

        detailsVO.setAcceptanceInfo(acceptanceInfo);
    }
    
    /**
     * 填充维修项目明细
     */
    private void fillRepairItemDetails(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        
    }
    
    /**
     * 填充维修图片明细
     */
    private void fillRepairPictures(MtcRepairTask repairTask, RepairTaskDetailsVO detailsVO) {
        
    }

    @Override
    public MtcTaskListResultVO queryRepairTaskList(RepairTaskListQueryDTO queryDTO) {
        MtcTaskListResultVO resultVO = new MtcTaskListResultVO();

        // 从Session获取登录用户信息
        // LoginUser loginUser = SessionUtils.getLoginUser();
        // if (StringUtils.isBlank(queryDTO.getOrgId())) {
        // queryDTO.setOrgId(loginUser.getUser().getOrgId().toString());
        // }

        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        // 1.查询数据 - 使用优化后的方法
        List<RepairTaskProcessListVO> list = tableRepairTaskService.queryRepairTaskListByActivityInstance(queryDTO, SessionUtils.getTenantId().intValue());
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询所有活动节点定义
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.findAll();
            // 查询所有活动状态定义
            List<ActivityStatus> activityStatuses = tableActivityStatusService.findAll();
            list.forEach(
                    item -> {
                        // 设置活动节点名称
                        setActivityNameByCode(item, activityDefinitions);
                        // 设置活动状态名称
                        setStatusNameByCode(item, activityStatuses);
                    });
        }
        PageInfo<RepairTaskProcessListVO> pageInfo = new PageInfo<>(list);
        BasePageVO<RepairTaskProcessListVO> pageVO = BasePageVO.of(list, pageInfo);
        resultVO.setPageInfo(pageVO);

        // 2.查询各环节任务数量
        if (queryDTO.getCurrentActivityCode() != null) {
            // 使用入参中的currentActivityCode查询活动实例表，按照statusCode分组统计任务数量
            Map<String, Long> statusCounts = tableActivityInstanceService.countByToActivityCodeGroupByStatusCode(
                    queryDTO.getCurrentActivityCode());
            resultVO.setStatusCounts(statusCounts);
        }

        return resultVO;
    }

    @Override
    public void saveRepairTask(Long id, RepairTaskUpdateDTO repairTaskUpdateDTO) {
        // 1.查询数据
        MtcRepairTask repairTask = tableRepairTaskService.selectById(id);
        if (repairTask == null) {
            throw new RuntimeException("未找到该数据");
        }
        
        // 2.更新数据
        BeanUtils.copyProperties(repairTaskUpdateDTO, repairTask);
        repairTask.setUpdatedTime(new Date());
        repairTask.setUpdateBy(SessionUtils.getUsername());
        tableRepairTaskService.updateSelectiveById(repairTask);
    }

    /**
     * 处理维修任务相关的图片和视频文件
     * 公共方法，供多个服务共用图片处理逻辑
     *
     * @param taskNo 任务编号
     * @param mediaTypeMap 图片/视频类型映射，键为图片类型，值为图片URL列表
     * @param operatorName 操作人用户名
     * @throws BusinessException 处理过程中的业务异常
     */
    @Override
    public void processMediaFiles(String taskNo, Map<BigDecimal, List<String>> mediaTypeMap, String operatorName) {
        log.info("开始处理维修任务图片和视频，任务编号：{}", taskNo);

        try {
            // 获取所有现有图片
            List<MtcVehicleRepairPic> allVehicleRepairPics = tableVehicleRepairPicService.selectByTaskNo(taskNo);
            log.debug("获取到现有图片/视频数量：{}", allVehicleRepairPics.size());

            // 收集需要插入和删除的图片/视频
            List<MtcVehicleRepairPic> insertPicList = new ArrayList<>();
            List<Long> deletePicIds = new ArrayList<>();

            // 处理每种类型的图片/视频
            mediaTypeMap.forEach((picType, newMediaList) -> {
                try {
                    // 获取要插入的图片/视频
                    List<String> insertMediaUrls = RepairPicUtil.getInsertPics(allVehicleRepairPics, newMediaList, picType);
                    if (CollectionUtils.isNotEmpty(insertMediaUrls)) {
                        List<MtcVehicleRepairPic> typedInsertList = RepairPicUtil.transferStringToVehicleRepairPic(
                                insertMediaUrls, picType, taskNo, operatorName);
                        insertPicList.addAll(typedInsertList);
                        log.debug("类型[{}]需要新增{}个图片/视频", picType, typedInsertList.size());
                    }

                    // 获取要删除的图片/视频
                    List<MtcVehicleRepairPic> typedDeleteList = RepairPicUtil.getDeletePics(allVehicleRepairPics, newMediaList, picType);
                    if (CollectionUtils.isNotEmpty(typedDeleteList)) {
                        List<Long> typedDeleteIds = typedDeleteList.stream()
                                .map(MtcVehicleRepairPic::getId)
                                .collect(Collectors.toList());
                        deletePicIds.addAll(typedDeleteIds);
                        log.debug("类型[{}]需要删除{}个图片/视频", picType, typedDeleteIds.size());
                    }
                } catch (Exception e) {
                    log.error("处理类型[{}]的图片/视频时发生错误：{}", picType, e.getMessage(), e);
                    // 这里不抛出异常，继续处理其他类型的图片/视频
                }
            });

            // 批量删除图片/视频（先删除再插入，避免主键冲突）
            if (CollectionUtils.isNotEmpty(deletePicIds)) {
                try {
                    int deleteCount = tableVehicleRepairPicService.delMaterialPic(deletePicIds);
                    log.info("成功删除{}个图片/视频", deleteCount);
                } catch (Exception e) {
                    log.error("批量删除图片/视频时发生错误：{}", e.getMessage(), e);
                    throw new BusinessException("删除图片/视频失败：" + e.getMessage());
                }
            }

            // 批量插入图片/视频
            if (CollectionUtils.isNotEmpty(insertPicList)) {
                try {
                    int insertCount = tableVehicleRepairPicService.batchInsert(insertPicList);
                    log.info("成功插入{}个图片/视频", insertCount);
                } catch (Exception e) {
                    log.error("批量插入图片/视频时发生错误：{}", e.getMessage(), e);
                    throw new BusinessException("插入图片/视频失败：" + e.getMessage());
                }
            }

            log.info("维修任务图片和视频处理完成，任务编号：{}", taskNo);
        } catch (Exception e) {
            log.error("处理维修任务图片和视频时发生未预期的错误：{}", e.getMessage(), e);
            throw new BusinessException("处理图片/视频失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void selectRepairDepot(Long taskId, String repairDepotId) {
        log.info("选择修理厂并更新维修任务，任务ID: {}, 修理厂ID: {}", taskId, repairDepotId);
        try {
            // 1. 参数校验
            if (taskId == null) {
                throw new BusinessException("维修任务ID不能为空");
            }
            if (null == repairDepotId) {
                throw new BusinessException("修理厂ID不能为空");
            }

            // 2. 查询维修任务
            MtcRepairTask repairTask = tableRepairTaskService.selectById(taskId);
            if (repairTask == null) {
                throw new BusinessException("未找到对应的维修任务");
            }

            // 3. 检查任务状态是否允许选择修理厂
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairTask.getTaskNo());
            if (null == workflowInstance) {
                throw new BusinessException("未找到对应的工作流实例");
            }
            if (workflowInstance.getCurrentActivityCode().equals("") && workflowInstance.getStatusCode().equals("")) {
                throw new BusinessException("任务状态不允许选择修理厂");
            }
            
            // 4. 查询修理厂信息
            MtcRepairDepotInfo repairDepotInfo = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);
            if (repairDepotInfo == null) {
                throw new BusinessException("未找到对应的修理厂");
            }

            // 5. 更新维修任务信息
            repairTask.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            repairTask.setRepairDepotName(repairDepotInfo.getRepairDepotName());
            repairTask.setRepairDepotOrgId(repairDepotInfo.getRepairDepotOrgId());
            repairTask.setRepairDepotSapCode(repairDepotInfo.getRepairDepotSapCode());
            repairTask.setRepairGrade(repairDepotInfo.getRepairDepotGrade());
            repairTask.setRepairGrade(repairDepotInfo.getRepairDepotGrade());
            repairTask.setTaxRate(repairDepotInfo.getTaxRate());

            tableRepairTaskService.updateSelectiveById(repairTask, SessionUtils.getUsername());
            
            // 6. 启动或更新工作流
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("SELECT_REPAIR_DEPOT");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            workflowService.processNode(workflowInstance.getId(), processDTO);
            log.info("选择修理厂成功，任务ID: {}, 修理厂: {}", taskId, repairDepotInfo.getRepairDepotName());
        } catch (BusinessException e) {
            log.error("选择修理厂失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("选择修理厂失败: {}", e.getMessage(), e);
            throw new BusinessException("选择修理厂失败: " + e.getMessage());
        }
    }

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRepairRemark(RepairRemarkDTO repairRemarkDTO) {
        log.info("添加维修备注，参数: {}", repairRemarkDTO);
        
        // 参数校验
        if (repairRemarkDTO == null || StringUtils.isBlank(repairRemarkDTO.getTaskNo())) {
            throw new BusinessException("维修任务编号不能为空");
        }
        
        if (StringUtils.isBlank(repairRemarkDTO.getRemark())) {
            throw new BusinessException("备注内容不能为空");
        }
        
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(repairRemarkDTO.getTaskNo());
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + repairRemarkDTO.getTaskNo());
        }
        
        // 获取当前登录用户
        String username = SessionUtils.getUsername();
        
        // 创建备注实体
        MtcRepairRemark repairRemark = new MtcRepairRemark();
        repairRemark.setTaskNo(repairRemarkDTO.getTaskNo());
        repairRemark.setRepairStage(repairRemarkDTO.getRepairStage());
        repairRemark.setRemark(repairRemarkDTO.getRemark());
        repairRemark.setCreatedTime(new Date());
        repairRemark.setCreateBy(username);
        repairRemark.setUpdatedTime(new Date());
        repairRemark.setUpdateBy(username);
        
        // 保存备注
        try {
            tableRepairRemarkService.insert(repairRemark);
            
            // 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(repairTask.getId());
            operatorLog.setOpeContent("添加备注：" + repairRemarkDTO.getRemark());
            operatorLog.setRemark(repairRemarkDTO.getRepairStage().toString());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setCreateBy(username);
            tableOperatorLogService.insertSelective(operatorLog);
            
            // // 清除缓存
            // String redisKey = "REPAIR_REMARK_" + repairRemarkDTO.getTaskNo();
            // redisTemplate.delete(redisKey);
            // redisTemplate.opsForSet().add(redisKey, username);
            
            log.info("添加维修备注成功，任务编号: {}, 备注ID: {}", repairRemarkDTO.getTaskNo(), repairRemark.getId());
        } catch (Exception e) {
            log.error("添加维修备注失败，任务编号: {}, 错误信息: {}", repairRemarkDTO.getTaskNo(), e.getMessage(), e);
            throw new BusinessException("添加维修备注失败: " + e.getMessage());
        }
    }

    /**
     * 删除维修备注
     *
     * @param id 备注ID
     * @param repairStage 维修阶段
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRepairRemark(Long id, String repairStage) {
        log.info("删除维修备注，备注ID: {}, 维修阶段: {}", id, repairStage);
        
        // 参数校验
        if (id == null) {
            throw new BusinessException("备注ID不能为空");
        }
        
        // 查询备注信息
        MtcRepairRemark repairRemark = tableRepairRemarkService.selectById(id);
        if (repairRemark == null) {
            throw new BusinessException("未找到备注信息，备注ID: " + id);
        }
        
        // 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(repairRemark.getTaskNo());
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + repairRemark.getTaskNo());
        }
        
        // 获取当前登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();
        String username = loginUser.getUser().getUsername();
        
        // 检查是否是备注创建人
        if (!repairRemark.getCreateBy().equals(username)) {
            throw new BusinessException("该备注为其他用户创建，无法删除");
        }
        
        try {
            // 删除备注
            tableRepairRemarkService.deleteById(id);
            
            // 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(repairTask.getId());
            operatorLog.setOpeContent("删除了备注：" + repairRemark.getRemark());
            operatorLog.setRemark(repairStage);
            operatorLog.setCreatedTime(new Date());
            operatorLog.setCreateBy(username);
            tableOperatorLogService.insertSelective(operatorLog);
            
            log.info("删除维修备注成功，备注ID: {}", id);
        } catch (Exception e) {
            log.error("删除维修备注失败，备注ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new BusinessException("删除维修备注失败: " + e.getMessage());
        }
    }
}
