package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.model.dto.OperateLogQueryDTO;
import com.extracme.saas.autocare.model.vo.OperateLogVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 操作日志服务接口
 */
public interface OperateLogService {

    /**
     * 分页查询操作日志列表
     *
     * @param queryDTO 查询参数
     * @return 操作日志列表
     */
    BasePageVO<OperateLogVO> getOperateLogList(OperateLogQueryDTO queryDTO);

    /**
     * 异步记录操作日志
     *
     * @param moduleType 模块类型
     * @param operateType 操作类型
     * @param content 操作内容描述
     * @param miscDesc 备注
     */
    void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                              OperateLogOperateTypeEnum operateType,
                              String content,
                              String miscDesc);

    /**
     * 异步记录操作日志（简化版本，无备注）
     *
     * @param moduleType 模块类型
     * @param operateType 操作类型
     * @param content 操作内容描述
     */
    void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                              OperateLogOperateTypeEnum operateType,
                              String content);

    /**
     * 异步记录操作日志（带用户信息参数）
     *
     * @param moduleType 模块类型
     * @param operateType 操作类型
     * @param content 操作内容描述
     * @param miscDesc 备注
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     */
    void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                              OperateLogOperateTypeEnum operateType,
                              String content,
                              String miscDesc,
                              Long userId,
                              String username,
                              Long tenantId);

    /**
     * 异步记录操作日志（带用户信息参数，简化版本，无备注）
     *
     * @param moduleType 模块类型
     * @param operateType 操作类型
     * @param content 操作内容描述
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     */
    void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                              OperateLogOperateTypeEnum operateType,
                              String content,
                              Long userId,
                              String username,
                              Long tenantId);
}
