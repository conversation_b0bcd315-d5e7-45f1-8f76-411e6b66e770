package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableOrgInfoService;
import com.extracme.saas.autocare.service.OrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织机构服务实现类
 */
@Slf4j
@Service
public class OrgServiceImpl implements OrgService {

    @Autowired
    private TableOrgInfoService orgInfoRepository;

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getOrgCombo() {
        // 调用根据租户ID查询的方法，传入null表示使用当前登录用户的租户ID
        return getOrgComboByTenantId(null);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getOrgComboByTenantId(Long tenantId) {
        try {
            // 保存当前租户上下文
            Long currentTenantId = TenantContextHolder.getTenantId();

            try {
                // 如果传入的租户ID不为空，则设置租户上下文
                if (tenantId != null) {
                    TenantContextHolder.setTenant(tenantId);
                }

                // 查询组织机构
                List<MtcOrgInfo> orgs = orgInfoRepository.findValidOrgs();
                List<ComboVO<String>> result = new ArrayList<>();

                for (MtcOrgInfo org : orgs) {
                    ComboVO<String> vo = new ComboVO<>();
                    vo.setId(org.getOrgId());
                    vo.setValue(org.getOrgName());
                    result.add(vo);
                }
                return result;
            } finally {
                // 恢复原始租户上下文
                if (tenantId != null && !tenantId.equals(currentTenantId)) {
                    if (currentTenantId != null) {
                        TenantContextHolder.setTenant(currentTenantId);
                    } else {
                        TenantContextHolder.clear();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取组织机构下拉列表失败", e);
            throw new RuntimeException("获取组织机构下拉列表失败");
        }
    }
}
