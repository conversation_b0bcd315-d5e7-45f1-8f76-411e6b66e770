package com.extracme.saas.autocare.mapper.extend;

import java.util.List;

import javax.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcRepairTaskMapper;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;

/**
 * 维修任务扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcRepairTaskExtendMapper extends MtcRepairTaskMapper {

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "RepairTaskProcessListResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "task_no", property = "taskNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "org_name", property = "orgName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_no", property = "vehicleNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_model_info", property = "vehicleModelInfo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR),
            @Result(column = "insurance_company_name", property = "insuranceCompanyName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_type_id", property = "repairTypeId", jdbcType = JdbcType.INTEGER),
            @Result(column = "repair_type_name", property = "repairTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_grade", property = "repairGrade", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_depot_id", property = "repairDepotId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_depot_name", property = "repairDepotName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_create_time", property = "taskCreateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "task_inflow_time", property = "taskInflowTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "vehicle_recive_time", property = "vehicleReciveTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "vehicle_check_time", property = "vehicleCheckTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "current_activity_code", property = "currentActivityCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "current_activity_name", property = "currentActivityName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status_code", property = "statusCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status_name", property = "statusName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "renttype", property = "renttype", jdbcType = JdbcType.INTEGER),
            @Result(column = "fact_operate_tag", property = "factOperateTag", jdbcType = JdbcType.INTEGER),
            @Result(column = "is_used_applets", property = "isUsedApplets", jdbcType = JdbcType.VARCHAR),
            @Result(column = "property_status", property = "propertyStatus", jdbcType = JdbcType.INTEGER),
            @Result(column = "product_line", property = "productLine", jdbcType = JdbcType.INTEGER),
            @Result(column = "sub_product_line", property = "subProductLine", jdbcType = JdbcType.INTEGER)
    })
    List<RepairTaskProcessListVO> selectProcessList(SelectStatementProvider selectStatement);
}
