package com.extracme.saas.autocare.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.service.OperateLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志记录工具类
 *
 * 提供便捷的操作日志记录方法，支持异步记录
 */
@Slf4j
@Component
public class OperateLogUtil {

    private static OperateLogService operateLogService;

    @Autowired
    public void setOperateLogService(OperateLogService operateLogService) {
        OperateLogUtil.operateLogService = operateLogService;
    }

    /**
     * 记录用户管理相关操作日志
     *
     * @param operateType 操作类型
     * @param content 操作内容描述
     */
    public static void recordUserLog(OperateLogOperateTypeEnum operateType, String content) {
        recordLog(OperateLogModuleTypeEnum.USER_MANAGEMENT, operateType, content);
    }

    /**
     * 记录用户管理相关操作日志（带备注）
     *
     * @param operateType 操作类型
     * @param content 操作内容描述
     * @param miscDesc 备注
     */
    public static void recordUserLog(OperateLogOperateTypeEnum operateType, String content, String miscDesc) {
        recordLog(OperateLogModuleTypeEnum.USER_MANAGEMENT, operateType, content, miscDesc);
    }

    /**
     * 记录系统管理相关操作日志
     *
     * @param operateType 操作类型
     * @param content 操作内容描述
     */
    public static void recordSystemLog(OperateLogOperateTypeEnum operateType, String content) {
        recordLog(OperateLogModuleTypeEnum.SYSTEM_MANAGEMENT, operateType, content);
    }

    /**
     * 记录系统管理相关操作日志（带备注）
     *
     * @param operateType 操作类型
     * @param content 操作内容描述
     * @param miscDesc 备注
     */
    public static void recordSystemLog(OperateLogOperateTypeEnum operateType, String content, String miscDesc) {
        recordLog(OperateLogModuleTypeEnum.SYSTEM_MANAGEMENT, operateType, content, miscDesc);
    }

    /**
     * 记录操作日志
     *
     * @param moduleType 模块类型
     * @param operateType 操作类型
     * @param content 操作内容描述
     */
    public static void recordLog(OperateLogModuleTypeEnum moduleType,
                                OperateLogOperateTypeEnum operateType,
                                String content) {
        recordLog(moduleType, operateType, content, null);
    }

    /**
     * 记录操作日志（带备注）
     *
     * @param moduleType 模块类型
     * @param operateType 操作类型
     * @param content 操作内容描述
     * @param miscDesc 备注
     */
    public static void recordLog(OperateLogModuleTypeEnum moduleType,
                                OperateLogOperateTypeEnum operateType,
                                String content,
                                String miscDesc) {
        log.info("开始记录操作日志：模块={}, 操作={}, 内容={}, 备注={}, 线程={}",
                moduleType.getName(), operateType.getName(), content, miscDesc, Thread.currentThread().getName());

        if (operateLogService == null) {
            log.error("OperateLogService未初始化，无法记录操作日志");
            return;
        }

        try {
            // 尝试获取当前登录用户信息
            log.info("尝试获取当前登录用户信息，线程={}", Thread.currentThread().getName());
            LoginUser loginUser = SessionUtils.getLoginUser();

            if (loginUser != null && loginUser.getUser() != null) {
                log.info("成功获取用户信息：userId={}, username={}, tenantId={}",
                        loginUser.getUser().getId(), loginUser.getUser().getUsername(), loginUser.getUser().getTenantId());

                // 如果能获取到用户信息，使用带用户信息参数的方法
                operateLogService.recordOperateLogAsync(moduleType, operateType, content, miscDesc,
                        loginUser.getUser().getId(),
                        loginUser.getUser().getUsername(),
                        loginUser.getUser().getTenantId());

                log.info("已调用带用户信息参数的异步记录方法");
            } else {
                log.warn("无法获取当前登录用户信息：loginUser={}, user={}",
                        loginUser, loginUser != null ? loginUser.getUser() : null);

                // 如果无法获取用户信息，使用原有方法（可能在异步线程中会失败，但保持向后兼容）
                log.warn("使用原有方法记录操作日志");
                operateLogService.recordOperateLogAsync(moduleType, operateType, content, miscDesc);

                log.info("已调用原有异步记录方法");
            }
        } catch (Exception e) {
            log.error("记录操作日志时获取用户信息失败，使用原有方法：模块={}, 操作={}, 内容={}",
                     moduleType.getName(), operateType.getName(), content, e);

            try {
                // 降级处理：使用原有方法
                operateLogService.recordOperateLogAsync(moduleType, operateType, content, miscDesc);
                log.info("降级处理：已调用原有异步记录方法");
            } catch (Exception fallbackException) {
                log.error("降级处理也失败了：模块={}, 操作={}, 内容={}",
                         moduleType.getName(), operateType.getName(), content, fallbackException);
            }
        }

        log.info("操作日志记录调用完成：模块={}, 操作={}, 内容={}",
                moduleType.getName(), operateType.getName(), content);
    }

    // 便捷方法：用户创建
    public static void recordUserCreate(String username) {
        recordUserLog(OperateLogOperateTypeEnum.CREATE, "创建用户：" + username);
    }

    // 便捷方法：用户更新
    public static void recordUserUpdate(String username) {
        recordUserLog(OperateLogOperateTypeEnum.UPDATE, "更新用户：" + username);
    }

    // 便捷方法：用户删除
    public static void recordUserDelete(String username) {
        recordUserLog(OperateLogOperateTypeEnum.DELETE, "删除用户：" + username);
    }

    // 便捷方法：角色创建
    public static void recordRoleCreate(String roleName) {
        recordSystemLog(OperateLogOperateTypeEnum.CREATE, "创建角色：" + roleName);
    }

    // 便捷方法：角色更新
    public static void recordRoleUpdate(String roleName) {
        recordSystemLog(OperateLogOperateTypeEnum.UPDATE, "更新角色：" + roleName);
    }

    // 便捷方法：角色删除
    public static void recordRoleDelete(String roleName) {
        recordSystemLog(OperateLogOperateTypeEnum.DELETE, "删除角色：" + roleName);
    }

    // ==================== 增强的详细日志记录方法 ====================

    /**
     * 记录用户创建详细日志
     *
     * @param mobile 手机号
     * @param nickname 姓名
     */
    public static void recordUserCreateDetailed(String mobile, String nickname) {
        String content = String.format("创建用户：手机号【%s】姓名【%s】",
                                      mobile != null ? mobile : "",
                                      nickname != null ? nickname : "");
        recordUserLog(OperateLogOperateTypeEnum.CREATE, content);
    }

    /**
     * 记录用户删除详细日志
     *
     * @param mobile 手机号
     * @param nickname 姓名
     */
    public static void recordUserDeleteDetailed(String mobile, String nickname) {
        String content = String.format("删除用户：手机号【%s】姓名【%s】",
                                      mobile != null ? mobile : "",
                                      nickname != null ? nickname : "");
        recordUserLog(OperateLogOperateTypeEnum.DELETE, content);
    }

    /**
     * 记录用户更新详细日志（带变更对比）
     *
     * @param oldUser 更新前的用户信息
     * @param newUser 更新后的用户信息
     */
    public static void recordUserUpdateDetailed(SysUser oldUser, SysUser newUser) {
        if (oldUser == null || newUser == null) {
            return;
        }

        // 构建用户标识信息
        String userIdentity = String.format("手机号【%s】姓名【%s】",
                                           oldUser.getMobile() != null ? oldUser.getMobile() : "",
                                           oldUser.getNickname() != null ? oldUser.getNickname() : "");

        // 检查状态变更
        if (isStatusChanged(oldUser.getStatus(), newUser.getStatus())) {
            String statusContent = String.format("更新用户：%s为%s状态",
                                                userIdentity,
                                                newUser.getStatus() == 1 ? "启用" : "停用");
            recordUserLog(OperateLogOperateTypeEnum.UPDATE, statusContent);
            return;
        }

        // 对比字段变更
        List<String> changes = compareUserFields(oldUser, newUser);
        if (!changes.isEmpty()) {
            String changesText = String.join("，", changes);
            String content = String.format("更新用户：%s，修改内容：%s", userIdentity, changesText);
            recordUserLog(OperateLogOperateTypeEnum.UPDATE, content);
        }
    }

    /**
     * 记录权限分配详细日志
     *
     * @param roleName 角色名称
     * @param addedPermissions 新增的权限列表
     * @param removedPermissions 删除的权限列表
     */
    public static void recordPermissionAssignDetailed(String roleName,
                                                     List<SysPermission> addedPermissions,
                                                     List<SysPermission> removedPermissions) {
        StringBuilder content = new StringBuilder();
        content.append("更新角色【").append(roleName).append("】权限：");

        List<String> changes = new ArrayList<>();

        if (addedPermissions != null && !addedPermissions.isEmpty()) {
            String addedNames = addedPermissions.stream()
                    .map(SysPermission::getPermissionName)
                    .collect(Collectors.joining("、"));
            changes.add("新增权限【" + addedNames + "】");
        }

        if (removedPermissions != null && !removedPermissions.isEmpty()) {
            String removedNames = removedPermissions.stream()
                    .map(SysPermission::getPermissionName)
                    .collect(Collectors.joining("、"));
            changes.add("删除权限【" + removedNames + "】");
        }

        if (!changes.isEmpty()) {
            content.append(String.join("，", changes));
            recordSystemLog(OperateLogOperateTypeEnum.UPDATE, content.toString());
        }
    }

    /**
     * 记录角色基本信息更新日志
     *
     * @param oldRoleName 旧角色名称
     * @param newRoleName 新角色名称
     * @param oldDescription 旧描述
     * @param newDescription 新描述
     */
    public static void recordRoleInfoUpdateDetailed(String oldRoleName, String newRoleName,
                                                   String oldDescription, String newDescription) {
        List<String> changes = new ArrayList<>();

        if (!Objects.equals(oldRoleName, newRoleName)) {
            changes.add(String.format("角色名从【%s】改为【%s】",
                                    oldRoleName != null ? oldRoleName : "",
                                    newRoleName != null ? newRoleName : ""));
        }

        if (!Objects.equals(oldDescription, newDescription)) {
            changes.add(String.format("描述从【%s】改为【%s】",
                                    oldDescription != null ? oldDescription : "",
                                    newDescription != null ? newDescription : ""));
        }

        if (!changes.isEmpty()) {
            String content = "更新角色：" + String.join("，", changes);
            recordSystemLog(OperateLogOperateTypeEnum.UPDATE, content);
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 检查状态是否发生变更
     */
    private static boolean isStatusChanged(Integer oldStatus, Integer newStatus) {
        return !Objects.equals(oldStatus, newStatus);
    }

    /**
     * 对比用户字段变更
     */
    private static List<String> compareUserFields(SysUser oldUser, SysUser newUser) {
        List<String> changes = new ArrayList<>();

        // 手机号变更
        if (!Objects.equals(oldUser.getMobile(), newUser.getMobile())) {
            changes.add(String.format("手机号从【%s】改为【%s】",
                                    oldUser.getMobile() != null ? oldUser.getMobile() : "",
                                    newUser.getMobile() != null ? newUser.getMobile() : ""));
        }

        // 邮箱变更
        if (!Objects.equals(oldUser.getEmail(), newUser.getEmail())) {
            changes.add(String.format("邮箱从【%s】改为【%s】",
                                    oldUser.getEmail() != null ? oldUser.getEmail() : "",
                                    newUser.getEmail() != null ? newUser.getEmail() : ""));
        }

        // 姓名变更
        if (!Objects.equals(oldUser.getNickname(), newUser.getNickname())) {
            changes.add(String.format("姓名从【%s】改为【%s】",
                                    oldUser.getNickname() != null ? oldUser.getNickname() : "",
                                    newUser.getNickname() != null ? newUser.getNickname() : ""));
        }

        // 组织ID变更
        if (!Objects.equals(oldUser.getOrgId(), newUser.getOrgId())) {
            changes.add(String.format("组织ID从【%s】改为【%s】",
                                    oldUser.getOrgId() != null ? oldUser.getOrgId() : "",
                                    newUser.getOrgId() != null ? newUser.getOrgId() : ""));
        }

        // 审批层级变更
        if (!Objects.equals(oldUser.getApprovalLevel(), newUser.getApprovalLevel())) {
            changes.add(String.format("审批层级从【%s】改为【%s】",
                                    oldUser.getApprovalLevel() != null ? oldUser.getApprovalLevel() : "",
                                    newUser.getApprovalLevel() != null ? newUser.getApprovalLevel() : ""));
        }

        // 修理厂ID变更
        if (!Objects.equals(oldUser.getRepairDepotId(), newUser.getRepairDepotId())) {
            changes.add(String.format("修理厂ID从【%s】改为【%s】",
                                    oldUser.getRepairDepotId() != null ? oldUser.getRepairDepotId() : "",
                                    newUser.getRepairDepotId() != null ? newUser.getRepairDepotId() : ""));
        }

        // 账号类型变更
        if (!Objects.equals(oldUser.getAccountType(), newUser.getAccountType())) {
            String oldTypeDesc = getAccountTypeDesc(oldUser.getAccountType());
            String newTypeDesc = getAccountTypeDesc(newUser.getAccountType());
            changes.add(String.format("账号类型从【%s】改为【%s】", oldTypeDesc, newTypeDesc));
        }

        return changes;
    }

    /**
     * 获取账号类型描述
     */
    private static String getAccountTypeDesc(Integer accountType) {
        if (accountType == null) return "";
        switch (accountType) {
            case 0: return "超级管理员";
            case 1: return "运营人员";
            case 2: return "修理厂";
            default: return String.valueOf(accountType);
        }
    }
}
