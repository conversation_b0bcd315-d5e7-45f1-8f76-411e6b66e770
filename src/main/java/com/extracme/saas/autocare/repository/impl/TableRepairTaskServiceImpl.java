package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotInfoDynamicSqlSupport.mtcRepairDepotInfo;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.mtcRepairTask;
import static com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport.workflowInstance;
import static com.extracme.saas.autocare.mapper.base.ActivityInstanceDynamicSqlSupport.activityInstance;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairTaskExtendMapper;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 维修任务表数据访问服务实现
 */
@Repository
public class TableRepairTaskServiceImpl implements TableRepairTaskService {

    @Autowired
    private MtcRepairTaskExtendMapper mtcRepairTaskMapper;

    @Override
    public MtcRepairTask insert(MtcRepairTask record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcRepairTask insert(MtcRepairTask record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcRepairTaskMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcRepairTask record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairTask record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcRepairTaskMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtcRepairTask selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcRepairTaskMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public MtcRepairTask selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskDynamicSqlSupport.taskNo, isEqualTo(taskNo));
        Optional<MtcRepairTask> optionalTask = mtcRepairTaskMapper.selectOne(completer);
        return optionalTask.orElse(null);
    }

    @Override
    public List<MtcRepairTask> selectByVin(String vin) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskDynamicSqlSupport.vin, isEqualTo(vin))
                .orderBy(id.descending());
        return mtcRepairTaskMapper.select(completer);
    }

    @Override
    public List<RepairTaskProcessListVO> queryProcessList(RepairTaskProcessQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.vin,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.repairGrade,
                mtcRepairTask.repairDepotId,
                mtcRepairTask.repairDepotName,
                mtcRepairDepotInfo.repairDepotType,
                mtcRepairTask.taskCreateTime,
                mtcRepairTask.taskInflowTime,
                mtcRepairTask.vehicleReciveTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTask.renttype,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.isUsedApplets,
                mtcRepairTask.propertyStatus,
                mtcRepairTask.productLine,
                mtcRepairTask.subProductLine,
                mtcRepairTask.createBy,
                mtcRepairTask.createdTime,
                mtcRepairTask.updateBy,
                mtcRepairTask.updatedTime,
                workflowInstance.id.as("instanceId"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode
                )
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .leftJoin(mtcRepairDepotInfo).on(mtcRepairTask.repairDepotId, equalTo(mtcRepairDepotInfo.id))
                .where()
                .and(mtcRepairTask.vin, isEqualToWhenPresent(queryDTO.getVin()))
                .and(mtcRepairTask.vehicleNo, isEqualToWhenPresent(queryDTO.getVehicleNo()))
                .and(mtcRepairTask.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(mtcRepairTask.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getRepairDepotOrgId()))
                .and(mtcRepairTask.taskCreateTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getTaskCreateStartTime()))
                .and(mtcRepairTask.taskCreateTime, isLessThanOrEqualToWhenPresent(queryDTO.getTaskCreateEndTime()))
                .and(mtcRepairTask.taskInflowTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getTaskInflowStartTime()))
                .and(mtcRepairTask.taskInflowTime, isLessThanOrEqualToWhenPresent(queryDTO.getTaskInflowEndTime()))
                .and(mtcRepairTask.vehicleReciveTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getVehicleReciveStartTime()))
                .and(mtcRepairTask.vehicleReciveTime, isLessThanOrEqualToWhenPresent(queryDTO.getVehicleReciveEndTime()))
                .and(mtcRepairTask.vehicleCheckTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getVehicleCheckStartTime()))
                .and(mtcRepairTask.vehicleCheckTime, isLessThanOrEqualToWhenPresent(queryDTO.getVehicleCheckEndTime()))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTask.renttype, isEqualToWhenPresent(queryDTO.getRenttype()))
                .and(mtcRepairTask.orgId, isLikeWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(workflowInstance.currentActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()))
                .orderBy(mtcRepairTask.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectProcessList(selectStatement);
    }

    @Override
    public List<RepairTaskProcessListVO> queryRepairTaskListByActivityInstance(RepairTaskListQueryDTO queryDTO, Integer tenantId) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.vin,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.repairGrade,
                mtcRepairTask.repairDepotId,
                mtcRepairTask.repairDepotName,
                mtcRepairDepotInfo.repairDepotType,
                mtcRepairTask.taskCreateTime,
                mtcRepairTask.taskInflowTime,
                mtcRepairTask.vehicleReciveTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTask.renttype,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.isUsedApplets,
                mtcRepairTask.propertyStatus,
                mtcRepairTask.productLine,
                mtcRepairTask.subProductLine,
                mtcRepairTask.createBy,
                mtcRepairTask.createdTime,
                mtcRepairTask.updateBy,
                mtcRepairTask.updatedTime,
                workflowInstance.id.as("instanceId"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode
                )
                .from(activityInstance)
                .leftJoin(workflowInstance).on(activityInstance.instanceId, equalTo(workflowInstance.id))
                .leftJoin(mtcRepairTask).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .leftJoin(mtcRepairDepotInfo).on(mtcRepairTask.repairDepotId, equalTo(mtcRepairDepotInfo.id))
                .where()
                .and(workflowInstance.tenantId, isEqualTo(tenantId.intValue()))
                .and(mtcRepairTask.vin, isEqualToWhenPresent(queryDTO.getVin()))
                .and(mtcRepairTask.vehicleNo, isEqualToWhenPresent(queryDTO.getVehicleNo()))
                .and(mtcRepairTask.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(mtcRepairTask.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getRepairDepotOrgId()))
                .and(mtcRepairTask.taskCreateTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getTaskCreateStartTime()))
                .and(mtcRepairTask.taskCreateTime, isLessThanOrEqualToWhenPresent(queryDTO.getTaskCreateEndTime()))
                .and(mtcRepairTask.taskInflowTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getTaskInflowStartTime()))
                .and(mtcRepairTask.taskInflowTime, isLessThanOrEqualToWhenPresent(queryDTO.getTaskInflowEndTime()))
                .and(mtcRepairTask.vehicleReciveTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getVehicleReciveStartTime()))
                .and(mtcRepairTask.vehicleReciveTime, isLessThanOrEqualToWhenPresent(queryDTO.getVehicleReciveEndTime()))
                .and(mtcRepairTask.vehicleCheckTime, isGreaterThanOrEqualToWhenPresent(queryDTO.getVehicleCheckStartTime()))
                .and(mtcRepairTask.vehicleCheckTime, isLessThanOrEqualToWhenPresent(queryDTO.getVehicleCheckEndTime()))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTask.renttype, isEqualToWhenPresent(queryDTO.getRenttype()))
                .and(mtcRepairTask.orgId, isLikeWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(activityInstance.toActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()))
                .and(activityInstance.currentStatusCode, isEqualToWhenPresent(queryDTO.getStatusCode()))
                .orderBy(mtcRepairTask.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectProcessList(selectStatement);
    }

    @Override
    public String getOverTime(Long id) {
        // 实现获取任务是否超时的逻辑
        // 这里需要根据实际业务逻辑实现，例如：
        // 1. 获取任务的预计完成时间和当前时间
        // 2. 比较两个时间，判断是否超时
        // 3. 返回超时状态：0表示超时，1表示未超时

        // 示例实现，实际应根据业务需求调整
        MtcRepairTask task = selectById(Long.valueOf(id));
        if (task == null) {
            return "1"; // 默认不超时
        }

        // 获取任务的预计修理天数和送修时间
        // 假设MtcRepairTask中的expectedRepairDays字段是String类型
        String expectedRepairDaysStr = String.valueOf(task.getExpectedRepairDays());
        Date sendRepairTime = task.getSendRepairTime();

        if (StringUtils.isBlank(expectedRepairDaysStr) || sendRepairTime == null) {
            return "1"; // 如果没有预计修理天数或送修时间，默认不超时
        }

        try {
            int days = Integer.parseInt(expectedRepairDaysStr);
            Date now = new Date();

            // 计算预计完成时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sendRepairTime);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            Date expectedCompleteTime = calendar.getTime();

            // 判断是否超时
            if (now.after(expectedCompleteTime)) {
                return "0"; // 超时
            } else {
                return "1"; // 未超时
            }
        } catch (NumberFormatException e) {
            return "1"; // 如果预计修理天数格式不正确，默认不超时
        }
    }

    /**
     * 清除任务核损核价占有人
     * 将维修任务的核损核价任务占有人字段设置为null
     *
     * @param taskId 维修任务ID
     * @param operator 操作人
     * @return 更新结果，返回受影响的行数
     */
    @Override
    public int clearOwner(Long taskId, String operator) {
        if (taskId == null) {
            return 0;
        }
        
        if (StringUtils.isBlank(operator)) {
            return 0;
        }
        
        
        Date now = new Date();
        
        // 使用MyBatis动态SQL构建更新语句
        int result = mtcRepairTaskMapper.update(c -> 
            c.set(MtcRepairTaskDynamicSqlSupport.verificationLossTaskOperId).equalToNull()
             .set(MtcRepairTaskDynamicSqlSupport.updateBy).equalTo(operator)
             .set(MtcRepairTaskDynamicSqlSupport.updatedTime).equalTo(now)
             .where(MtcRepairTaskDynamicSqlSupport.id, isEqualTo(taskId))
        );
        return result;
    }
}
