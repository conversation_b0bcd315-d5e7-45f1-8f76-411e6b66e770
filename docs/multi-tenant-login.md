# 多租户登录功能说明

## 概述

本系统支持用户拥有多个商户账号的情况，提供了完整的多租户登录解决方案。

## 功能特性

### 1. 多商户账号检测
- 系统会自动检测用户是否关联了多个商户账号
- 如果用户只有一个商户账号，则自动登录到该账号
- 如果用户有多个商户账号，则要求用户选择具体的租户进行登录

### 2. 错误处理
- `MULTIPLE_TENANT_ACCOUNTS(220005)`: 用户存在多个商户账号，需要选择具体的租户进行登录
- `NO_TENANT_ACCOUNTS(220006)`: 用户未关联任何商户账号
- `TENANT_ACCESS_DENIED(220007)`: 用户无权限访问指定的租户

## API 接口

### 1. 短信验证码登录（支持多租户）

**接口地址**: `POST /api/auth/login/mobile`

**请求参数**:
```json
{
  "mobile": "***********",
  "code": "123456",
  "tenantId": 1  // 可选，当用户有多个商户账号时需要指定
}
```

**响应示例**:

成功响应:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
    "userId": 1,
    "username": "testuser",
    "mobile": "***********",
    "expireTime": "2025-05-26T09:30:00"
  }
}
```

多租户账号错误响应:
```json
{
  "code": 220005,
  "message": "用户存在多个商户账号，请选择具体的租户进行登录"
}
```

### 2. 查询用户商户账号列表

**接口地址**: `POST /api/auth/tenant-accounts`

**请求参数**:
```json
{
  "mobile": "***********"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "value": "租户A"
    },
    {
      "id": "2", 
      "value": "租户B"
    }
  ]
}
```

## 使用流程

### 场景1: 用户只有一个商户账号
1. 用户输入手机号和验证码
2. 调用登录接口，不传递 `tenantId`
3. 系统自动登录到唯一的商户账号

### 场景2: 用户有多个商户账号
1. 用户输入手机号和验证码
2. 调用登录接口，不传递 `tenantId`
3. 系统返回 `MULTIPLE_TENANT_ACCOUNTS` 错误
4. 前端调用查询商户账号列表接口
5. 用户选择具体的租户
6. 重新调用登录接口，传递选择的 `tenantId`
7. 登录成功

### 场景3: 用户直接指定租户登录
1. 用户输入手机号、验证码和租户ID
2. 调用登录接口，传递 `tenantId`
3. 系统验证用户是否有权限访问该租户
4. 登录成功或返回权限错误

## 前端实现建议

```javascript
// 1. 首次登录尝试
async function login(mobile, code, tenantId = null) {
  try {
    const response = await api.post('/auth/login/mobile', {
      mobile,
      code,
      tenantId
    });
    
    // 登录成功
    return response.data;
  } catch (error) {
    if (error.code === 220005) {
      // 多租户账号，需要选择
      const accounts = await getTenantAccounts(mobile);
      return { needSelectTenant: true, accounts };
    }
    throw error;
  }
}

// 2. 查询租户账号列表
async function getTenantAccounts(mobile) {
  const response = await api.post('/auth/tenant-accounts', { mobile });
  return response.data;
}

// 3. 选择租户后重新登录
async function loginWithTenant(mobile, code, tenantId) {
  return await login(mobile, code, tenantId);
}
```

## 数据库设计

系统通过以下方式支持多租户：

1. `sys_user` 表包含 `tenant_id` 字段，标识用户所属的租户
2. 同一个手机号可以在不同租户下创建不同的用户记录
3. 登录时根据手机号查询所有关联的租户ID
4. 验证用户对指定租户的访问权限

## 注意事项

1. 验证码验证通过后，如果发现多租户情况，验证码不会被标记为已使用，用户可以继续使用该验证码选择租户登录
2. 只有在最终登录成功后，验证码才会被标记为已使用
3. 系统会记录详细的登录日志，包括租户选择过程
4. 所有业务异常都会被 GlobalExceptionHandler 统一处理，返回标准的错误响应格式
