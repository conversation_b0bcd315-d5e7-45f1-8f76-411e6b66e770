# 操作日志记录问题排查指南

## 问题现象

使用 OperateLogUtil 工具类记录操作日志后，数据库中的 sys_operate_log 表没有生成任何日志记录。

## 可能原因分析

### 1. 异步方法执行问题
- **异步线程池配置问题**：异步任务执行器可能没有正确配置
- **异步方法没有被调用**：Spring AOP 代理可能没有生效
- **异步方法执行异常**：异步线程中发生异常但被捕获

### 2. 用户信息获取问题
- **Request 上下文缺失**：在异步线程中无法获取 Request 上下文
- **Session 信息丢失**：用户登录信息在异步线程中不可用
- **用户信息为空**：SessionUtils.getLoginUser() 返回 null

### 3. 数据库事务问题
- **事务传播问题**：异步方法中的事务可能没有正确传播
- **事务回滚**：数据库操作可能因为异常而回滚
- **连接池问题**：数据库连接可能存在问题

### 4. 配置问题
- **异步注解未生效**：@EnableAsync 可能没有配置
- **服务注入问题**：OperateLogService 可能没有正确注入
- **Mapper 配置问题**：MyBatis Mapper 可能存在配置问题

## 排查步骤

### 第一步：检查服务注入状态

```java
@Autowired
private OperateLogDebugTool debugTool;

// 检查服务状态
debugTool.checkServiceStatus();
```

### 第二步：测试直接数据库插入

```java
// 测试直接数据库插入，排除异步和用户信息问题
debugTool.testDirectDatabaseInsert();
```

### 第三步：测试带用户信息的异步方法

```java
// 测试带用户信息参数的异步方法
debugTool.testAsyncWithUserInfo();
```

### 第四步：测试工具类方法

```java
// 测试完整的工具类调用链路
debugTool.testUtilMethod();
```

### 第五步：查询数据库记录

```java
// 查询最近的操作日志记录
debugTool.queryRecentLogs();
```

### 第六步：运行完整测试

```java
// 运行所有测试
debugTool.runAllTests();
```

## 日志分析

### 关键日志点

1. **工具类调用日志**
   ```
   开始记录操作日志：模块=用户管理, 操作=新增, 内容=xxx, 备注=xxx, 线程=main
   ```

2. **用户信息获取日志**
   ```
   尝试获取当前登录用户信息，线程=main
   成功获取用户信息：userId=1, username=admin, tenantId=1
   ```

3. **异步方法执行日志**
   ```
   异步方法开始执行：模块=用户管理, 操作=新增, 内容=xxx, 操作人=admin, 租户ID=1, 线程=async-task-1
   ```

4. **数据库插入日志**
   ```
   开始插入操作日志：operateLog=xxx, operator=admin, 线程=async-task-1
   数据库插入执行完成，影响行数：1, 生成的ID：123
   ```

### 常见错误日志

1. **用户信息获取失败**
   ```
   无法获取当前登录用户信息：loginUser=null, user=null
   ```

2. **异步方法未执行**
   ```
   已调用带用户信息参数的异步记录方法
   // 但没有后续的异步方法执行日志
   ```

3. **数据库插入失败**
   ```
   插入操作日志时发生异常：operateLog=xxx, operator=xxx, 异常类型=xxx, 异常信息=xxx
   ```

## 修复方案

### 1. 确保异步配置正确

检查 `AsyncConfig` 配置：

```java
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {
    // 异步任务执行器配置
}
```

### 2. 修复用户信息传递

使用带用户信息参数的方法：

```java
// 在调用前获取用户信息
LoginUser loginUser = SessionUtils.getLoginUser();
if (loginUser != null && loginUser.getUser() != null) {
    operateLogService.recordOperateLogAsync(
        moduleType, operateType, content, miscDesc,
        loginUser.getUser().getId(),
        loginUser.getUser().getUsername(),
        loginUser.getUser().getTenantId()
    );
}
```

### 3. 添加事务注解

确保数据库操作有事务支持：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public SysOperateLog insert(SysOperateLog operateLog, String operator) {
    // 数据库插入操作
}
```

### 4. 增强错误处理

添加详细的异常处理和日志记录：

```java
try {
    // 操作日志记录逻辑
} catch (Exception e) {
    log.error("记录操作日志失败", e);
    // 不要重新抛出异常，避免影响主业务
}
```

## 验证修复效果

### 1. 检查日志输出

确认以下日志都能正常输出：
- 工具类调用日志
- 用户信息获取日志
- 异步方法执行日志
- 数据库插入日志

### 2. 检查数据库记录

查询 sys_operate_log 表：

```sql
SELECT * FROM sys_operate_log ORDER BY create_time DESC LIMIT 10;
```

### 3. 测试不同场景

- 主线程中调用
- 异步线程中调用
- 有用户信息的场景
- 无用户信息的场景

## 常见问题解决

### Q1: 异步方法没有执行
**A**: 检查 @EnableAsync 注解是否添加，异步任务执行器是否正确配置

### Q2: 用户信息获取失败
**A**: 在调用异步方法前先获取用户信息，然后作为参数传递

### Q3: 数据库插入失败
**A**: 检查数据库连接、表结构、字段映射是否正确

### Q4: 事务回滚导致数据丢失
**A**: 确保异步方法有独立的事务，使用 @Transactional 注解

### Q5: 日志级别问题
**A**: 确保日志级别设置为 INFO 或 DEBUG，能够看到详细的调试日志
