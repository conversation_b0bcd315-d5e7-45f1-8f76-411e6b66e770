# 操作日志功能说明

## 功能概述

操作日志功能用于记录系统中用户的重要操作行为，包括用户管理、角色管理等模块的增删改查操作。该功能支持异步记录，不影响主业务流程，并提供多租户权限控制的查询接口。

## 功能特性

### 1. 异步记录
- 使用 `@Async` 注解实现异步记录，不影响主业务性能
- 配置专用线程池处理异步任务
- 异常处理机制确保日志记录失败不影响主业务

### 2. 多租户支持
- 超级管理员（accountType=0）可查看所有租户的操作日志
- 普通用户只能查看自己租户的操作日志
- 自动记录操作人的租户ID

### 3. 模块化设计
- 支持多种模块类型：用户管理、系统管理
- 支持多种操作类型：新增、修改、删除、启用、禁用
- 可扩展的枚举设计，便于添加新的模块和操作类型

### 4. 详细日志记录（增强功能）
- **用户管理日志增强**：
  - 用户创建：记录手机号、姓名、用户名等详细信息
  - 用户更新：实现字段变更对比，记录具体修改内容
  - 用户删除：记录完整的用户标识信息
  - 状态变更：特别标识启用/停用操作

- **角色管理日志增强**：
  - 角色基本信息更新：区分角色名称、描述等字段变更
  - 权限分配：详细记录权限的新增和删除情况
  - 权限变更对比：自动计算权限差异并生成可读描述

- **智能变更对比**：
  - 自动对比更新前后的字段变化
  - 忽略审计字段（updateTime、updateBy等）
  - 格式化输出字段变更信息
  - 支持状态变更的特殊处理

## 数据库表结构

### sys_operate_log 表字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| tenant_id | BIGINT | 租户ID |
| module_type | INT | 模块类型（1：用户管理 2：系统管理） |
| operate_type | INT | 操作类型（1：新增 2：修改 3：删除 4：禁用 5：启用） |
| content | VARCHAR | 操作内容描述 |
| misc_desc | VARCHAR | 备注 |
| status | INT | 是否有效（0 无效 1 有效） |
| create_time | DATETIME | 创建时间 |
| create_by | VARCHAR | 创建人 |
| update_time | DATETIME | 修改时间 |
| update_by | VARCHAR | 修改人 |

## API 接口

### 查询操作日志列表

**接口地址：** `POST /api/v1/operate-logs/page`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "tenantId": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 10,
    "list": [
      {
        "id": 1,
        "tenantId": 1,
        "tenantName": "测试租户",
        "moduleType": 1,
        "moduleTypeName": "用户管理",
        "operateType": 1,
        "operateTypeName": "新增",
        "content": "创建用户：张三",
        "miscDesc": "系统自动记录",
        "status": 1,
        "createTime": "2024-01-01 12:00:00",
        "createBy": "admin"
      }
    ],
    "hasNextPage": true,
    "hasPreviousPage": false,
    "isFirstPage": true,
    "isLastPage": false
  }
}
```

## 使用方式

### 1. 在业务代码中记录日志

#### 方式一：使用增强的详细日志记录方法（推荐）

```java
// 记录用户创建详细日志
OperateLogUtil.recordUserCreateDetailed("15700091234", "张三");
// 输出：创建用户：手机号【15700091234】姓名【张三】

// 记录用户删除详细日志
OperateLogUtil.recordUserDeleteDetailed("15700091234", "张三");
// 输出：删除用户：手机号【15700091234】姓名【张三】

// 记录用户更新详细日志（带变更对比）
SysUser oldUser = ...; // 更新前的用户信息
SysUser newUser = ...; // 更新后的用户信息
OperateLogUtil.recordUserUpdateDetailed(oldUser, newUser);
// 输出示例：更新用户：手机号【15700091234】姓名【张三】，修改内容：手机号从【15700091111】改为【15700091234】，邮箱从【<EMAIL>】改为【<EMAIL>】
// 或者：更新用户：手机号【15700091234】姓名【张三】为启用状态

// 记录权限分配详细日志
List<SysPermission> addedPermissions = ...; // 新增的权限列表
List<SysPermission> removedPermissions = ...; // 删除的权限列表
OperateLogUtil.recordPermissionAssignDetailed("管理员角色", addedPermissions, removedPermissions);
// 输出：更新角色【管理员角色】权限：新增权限【用户查询、用户创建】，删除权限【系统配置、日志查看】

// 记录角色基本信息更新日志
OperateLogUtil.recordRoleInfoUpdateDetailed("旧角色名", "新角色名", "旧描述", "新描述");
// 输出：更新角色：角色名从【旧角色名】改为【新角色名】，描述从【旧描述】改为【新描述】
```

#### 方式二：使用简化的日志记录方法

```java
// 记录用户创建
OperateLogUtil.recordUserCreate("张三");

// 记录用户更新
OperateLogUtil.recordUserUpdate("张三");

// 记录用户删除
OperateLogUtil.recordUserDelete("张三");

// 记录角色创建
OperateLogUtil.recordRoleCreate("管理员角色");

// 记录角色更新
OperateLogUtil.recordRoleUpdate("管理员角色");

// 记录角色删除
OperateLogUtil.recordRoleDelete("管理员角色");

// 自定义日志记录
OperateLogUtil.recordLog(
    OperateLogModuleTypeEnum.USER_MANAGEMENT,
    OperateLogOperateTypeEnum.CREATE,
    "创建用户：张三",
    "通过批量导入创建"
);
```

#### 方式二：直接使用Service

```java
@Autowired
private OperateLogService operateLogService;

// 记录操作日志
operateLogService.recordOperateLogAsync(
    OperateLogModuleTypeEnum.USER_MANAGEMENT,
    OperateLogOperateTypeEnum.CREATE,
    "创建用户：张三",
    "系统自动记录"
);
```

### 2. 在Controller中查询日志

```java
@RestController
@RequestMapping("/api/v1/operate-logs")
public class SysOperateLogController {

    @Autowired
    private OperateLogService operateLogService;

    @PostMapping("/page")
    public Result<BasePageVO<OperateLogVO>> getOperateLogList(
            @Valid @RequestBody OperateLogQueryDTO queryDTO) {
        BasePageVO<OperateLogVO> pageInfo = operateLogService.getOperateLogList(queryDTO);
        return Result.success(pageInfo);
    }
}
```

## 配置说明

### 异步任务配置

系统已配置专用的异步任务执行器：

```java
@Configuration
public class AsyncConfig implements AsyncConfigurer {

    @Bean("taskExecutor")
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);           // 核心线程数
        executor.setMaxPoolSize(20);           // 最大线程数
        executor.setQueueCapacity(100);        // 队列容量
        executor.setThreadNamePrefix("async-task-");
        // ... 其他配置
        return executor;
    }
}
```

### 多租户权限控制

操作日志查询自动应用多租户权限控制：

- **超级管理员**：可查看所有租户的操作日志
- **普通用户**：只能查看自己租户的操作日志

权限判断基于 `SessionUtils.isSuperAdmin()` 方法。

## 扩展指南

### 添加新的模块类型

1. 在 `OperateLogModuleTypeEnum` 中添加新的枚举值
2. 在 `OperateLogUtil` 中添加对应的便捷方法

### 添加新的操作类型

1. 在 `OperateLogOperateTypeEnum` 中添加新的枚举值
2. 根据需要在 `OperateLogUtil` 中添加便捷方法

### 自定义日志内容

可以通过 `OperateLogUtil.recordLog()` 方法记录自定义的日志内容，支持灵活的内容描述和备注信息。

## 增强功能实现细节

### 1. 用户管理日志增强实现

#### 用户创建日志
```java
// 在UserService.createUser方法中
OperateLogUtil.recordUserCreateDetailed(createDTO.getMobile(), createDTO.getNickname());
// 生成日志：创建用户：手机号【15700091234】姓名【张三】
```

#### 用户更新日志
```java
// 在UserService.updateUser方法中
// 构建更新后的用户对象用于对比
SysUser updatedUser = new SysUser();
BeanUtils.copyProperties(existingUser, updatedUser);
// 设置更新后的字段值...
OperateLogUtil.recordUserUpdateDetailed(existingUser, updatedUser);
// 自动对比字段变更并生成详细日志
```

#### 字段变更对比逻辑
- 支持的对比字段：手机号、邮箱、姓名、组织ID、审批层级、修理厂ID、账号类型
- 状态变更特殊处理：优先检查状态变更，生成启用/停用日志
- 账号类型描述转换：0-超级管理员、1-运营人员、2-修理厂

### 2. 角色管理日志增强实现

#### 角色基本信息更新
```java
// 在RoleService.updateRole方法中
private void recordRoleInfoUpdateLog(SysRole oldRole, SysRole newRole) {
    String oldRoleName = oldRole.getRoleName();
    String newRoleName = newRole.getRoleName() != null ? newRole.getRoleName() : oldRole.getRoleName();
    // 对比角色名称和描述变更
    OperateLogUtil.recordRoleInfoUpdateDetailed(oldRoleName, newRoleName, oldDescription, newDescription);
}
```

#### 权限分配日志
```java
// 在RoleService.assignPermissions方法中
private void recordPermissionChangeLog(String roleName, List<Long> oldPermissionIds, List<Long> newPermissionIds) {
    // 计算新增和删除的权限ID
    List<Long> addedIds = newIds.stream().filter(id -> !oldIds.contains(id)).collect(Collectors.toList());
    List<Long> removedIds = oldIds.stream().filter(id -> !newIds.contains(id)).collect(Collectors.toList());

    // 查询权限名称并记录详细日志
    List<SysPermission> addedPermissions = getPermissionsByIds(addedIds);
    List<SysPermission> removedPermissions = getPermissionsByIds(removedIds);
    OperateLogUtil.recordPermissionAssignDetailed(roleName, addedPermissions, removedPermissions);
}
```

### 3. 工具类增强方法

#### 详细日志记录方法
- `recordUserCreateDetailed(mobile, nickname)`: 用户创建详细日志
- `recordUserDeleteDetailed(mobile, nickname)`: 用户删除详细日志
- `recordUserUpdateDetailed(oldUser, newUser)`: 用户更新变更对比日志
- `recordPermissionAssignDetailed(roleName, addedPermissions, removedPermissions)`: 权限分配详细日志
- `recordRoleInfoUpdateDetailed(oldRoleName, newRoleName, oldDescription, newDescription)`: 角色信息更新日志

#### 私有工具方法
- `compareUserFields(oldUser, newUser)`: 用户字段变更对比
- `isStatusChanged(oldStatus, newStatus)`: 状态变更检查
- `getAccountTypeDesc(accountType)`: 账号类型描述转换

### 4. 业务集成点

#### UserService集成
- `createUser`: 使用`recordUserCreateDetailed`记录详细创建信息
- `updateUser`: 使用`recordUserUpdateDetailed`记录字段变更对比

#### RoleService集成
- `updateRole`: 区分基本信息更新，使用`recordRoleInfoUpdateLog`
- `assignPermissions`: 权限变更对比，使用`recordPermissionChangeLog`

## 注意事项

1. **异步特性**：日志记录是异步的，可能存在轻微延迟
2. **异常处理**：日志记录失败不会影响主业务流程
3. **性能影响**：异步设计确保对主业务性能影响最小
4. **权限控制**：查询操作日志需要相应的权限，普通用户只能查看自己租户的日志
5. **数据量**：建议定期清理历史日志数据，避免表数据过大影响查询性能
6. **字段对比**：变更对比功能会忽略审计字段，只关注业务字段变更
7. **权限查询**：权限分配日志会批量查询权限名称，确保日志可读性
