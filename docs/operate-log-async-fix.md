# 操作日志异步记录问题修复说明

## 问题描述

在异步线程中无法获取登录用户信息，导致操作日志记录失败。具体表现为：

1. **异步线程无法获取Request上下文**：`SessionUtils.getLoginUser()` 和 `SessionUtils.getUsername()` 依赖于 `RequestContextHolder.getRequestAttributes()`，但在异步线程中无法获取到Request上下文。

2. **用户信息丢失**：异步方法 `recordOperateLogAsync` 在异步线程中执行时，无法获取当前登录用户信息，导致操作日志记录被跳过。

3. **错误信息**：
   ```
   无法获取当前登录用户信息，跳过操作日志记录
   当前线程没有绑定Request上下文，线程ID: xxx, 线程名称: async-task-1
   ```

## 修复方案

采用**用户信息显式传递**的方案，在调用异步操作日志记录时，将当前登录用户信息作为参数显式传递给异步方法。

### 1. 扩展 OperateLogService 接口

添加新的方法，接受用户信息作为参数：

```java
/**
 * 异步记录操作日志（带用户信息参数）
 */
void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType, 
                          OperateLogOperateTypeEnum operateType, 
                          String content, 
                          String miscDesc,
                          Long userId,
                          String username,
                          Long tenantId);

/**
 * 异步记录操作日志（带用户信息参数，简化版本，无备注）
 */
void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType, 
                          OperateLogOperateTypeEnum operateType, 
                          String content,
                          Long userId,
                          String username,
                          Long tenantId);
```

### 2. 实现新的异步方法

在 `OperateLogServiceImpl` 中实现新的异步方法：

```java
@Override
@Async
public void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType, 
                                 OperateLogOperateTypeEnum operateType, 
                                 String content, 
                                 String miscDesc,
                                 Long userId,
                                 String username,
                                 Long tenantId) {
    try {
        // 验证必要的用户信息
        if (userId == null || username == null || tenantId == null) {
            log.warn("用户信息不完整，跳过操作日志记录：userId={}, username={}, tenantId={}", 
                    userId, username, tenantId);
            return;
        }

        // 创建操作日志对象
        SysOperateLog operateLog = new SysOperateLog();
        operateLog.setTenantId(tenantId);
        operateLog.setModuleType(moduleType.getCode());
        operateLog.setOperateType(operateType.getCode());
        operateLog.setContent(content);
        operateLog.setMiscDesc(miscDesc);
        operateLog.setStatus(1); // 有效状态

        // 异步记录操作日志
        tableSysOperateLogService.insert(operateLog, username);

        log.debug("操作日志记录成功：模块={}, 操作={}, 内容={}, 操作人={}, 租户ID={}", 
                 moduleType.getName(), operateType.getName(), content, username, tenantId);

    } catch (Exception e) {
        log.error("记录操作日志失败：模块={}, 操作={}, 内容={}, 操作人={}, 租户ID={}", 
                 moduleType.getName(), operateType.getName(), content, username, tenantId, e);
    }
}
```

### 3. 修改 OperateLogUtil 工具类

修改工具类，在调用异步方法前获取用户信息并传递：

```java
public static void recordLog(OperateLogModuleTypeEnum moduleType,
                            OperateLogOperateTypeEnum operateType,
                            String content,
                            String miscDesc) {
    if (operateLogService == null) {
        log.warn("OperateLogService未初始化，无法记录操作日志");
        return;
    }

    try {
        // 尝试获取当前登录用户信息
        LoginUser loginUser = SessionUtils.getLoginUser();
        if (loginUser != null && loginUser.getUser() != null) {
            // 如果能获取到用户信息，使用带用户信息参数的方法
            operateLogService.recordOperateLogAsync(moduleType, operateType, content, miscDesc,
                    loginUser.getUser().getId(),
                    loginUser.getUser().getUsername(),
                    loginUser.getUser().getTenantId());
        } else {
            // 如果无法获取用户信息，使用原有方法（降级处理）
            log.warn("无法获取当前登录用户信息，使用原有方法记录操作日志");
            operateLogService.recordOperateLogAsync(moduleType, operateType, content, miscDesc);
        }
    } catch (Exception e) {
        log.error("记录操作日志时获取用户信息失败，使用原有方法：模块={}, 操作={}, 内容={}", 
                 moduleType.getName(), operateType.getName(), content, e);
        // 降级处理：使用原有方法
        operateLogService.recordOperateLogAsync(moduleType, operateType, content, miscDesc);
    }
}
```

### 4. 保持向后兼容

原有的方法仍然保留，但内部会调用新的带用户信息参数的方法：

```java
@Override
@Async
public void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType, 
                                 OperateLogOperateTypeEnum operateType, 
                                 String content, 
                                 String miscDesc) {
    try {
        // 获取当前登录用户信息
        LoginUser loginUser = SessionUtils.getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            log.warn("无法获取当前登录用户信息，跳过操作日志记录");
            return;
        }

        // 调用带用户信息参数的方法
        recordOperateLogAsync(moduleType, operateType, content, miscDesc,
                loginUser.getUser().getId(),
                loginUser.getUser().getUsername(),
                loginUser.getUser().getTenantId());

    } catch (Exception e) {
        log.error("记录操作日志失败：模块={}, 操作={}, 内容={}", 
                 moduleType.getName(), operateType.getName(), content, e);
    }
}
```

## 使用方法

### 1. 推荐使用方式（工具类）

继续使用 `OperateLogUtil` 工具类，它会自动处理用户信息的获取和传递：

```java
// 原有的使用方式不变
OperateLogUtil.recordUserLog(OperateLogOperateTypeEnum.CREATE, "创建用户：张三");
OperateLogUtil.recordSystemLog(OperateLogOperateTypeEnum.UPDATE, "更新角色：管理员");
```

### 2. 直接使用Service（在已知用户信息的情况下）

如果在某些场景下已经有用户信息，可以直接使用新的带用户信息参数的方法：

```java
@Autowired
private OperateLogService operateLogService;

// 使用带用户信息参数的方法
operateLogService.recordOperateLogAsync(
    OperateLogModuleTypeEnum.USER_MANAGEMENT,
    OperateLogOperateTypeEnum.CREATE,
    "创建用户：张三",
    "系统自动记录",
    1L,           // 用户ID
    "admin",      // 用户名
    1L            // 租户ID
);
```

## 修复效果

1. **异步线程中能正常记录操作日志**：新的带用户信息参数的方法不依赖Request上下文，在异步线程中也能正常工作。

2. **保持向后兼容**：原有的API和使用方式不变，现有代码无需修改。

3. **自动降级处理**：如果无法获取用户信息，会自动降级到原有方法，确保系统稳定性。

4. **详细的日志记录**：增加了详细的日志记录，便于问题排查和监控。

## 测试验证

可以使用 `OperateLogAsyncDemo` 类来验证修复效果：

```java
@Autowired
private OperateLogAsyncDemo demo;

// 运行演示
demo.runAllDemos();
```

该演示会在主线程和异步线程中分别测试原有方法和新方法的表现，验证修复的有效性。
